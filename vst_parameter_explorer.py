#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔬 VST参数探索器
尝试不同的方法来控制VST插件参数
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class VSTParameterExplorer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔬 VST参数探索器")
        self.root.geometry("1000x700")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 探索方法选择
        method_frame = ttk.LabelFrame(main_frame, text="🔬 探索方法", padding="10")
        method_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 基本信息
        info_row = ttk.Frame(method_frame)
        info_row.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_row, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(info_row, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(info_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(info_row, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # 探索按钮
        btn_row = ttk.Frame(method_frame)
        btn_row.pack(fill=tk.X)
        
        self.method1_btn = ttk.Button(btn_row, text="🔍 方法1: 标准参数", 
                                     command=self.explore_standard_params, state="disabled")
        self.method1_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.method2_btn = ttk.Button(btn_row, text="🔍 方法2: 直接设置", 
                                     command=self.explore_direct_settings, state="disabled")
        self.method2_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.method3_btn = ttk.Button(btn_row, text="🔍 方法3: 参数名称", 
                                     command=self.explore_named_params, state="disabled")
        self.method3_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.method4_btn = ttk.Button(btn_row, text="🔍 方法4: 滤镜重建", 
                                     command=self.explore_filter_recreation, state="disabled")
        self.method4_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 探索结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🔬 VST参数探索器启动")
        self.log("这个工具将尝试多种方法来控制VST插件参数")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有探索按钮
        self.method1_btn.config(state="normal")
        self.method2_btn.config(state="normal")
        self.method3_btn.config(state="normal")
        self.method4_btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有探索按钮
        self.method1_btn.config(state="disabled")
        self.method2_btn.config(state="disabled")
        self.method3_btn.config(state="disabled")
        self.method4_btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
            
    def explore_standard_params(self):
        """方法1: 探索标准参数名称"""
        self.log("🔍 方法1: 尝试标准VST参数名称")
        self.log("=" * 50)
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        # 常见的VST参数名称
        common_params = [
            # Graillon相关
            ("pitch", [-12.0, 0.0, 12.0]),
            ("Pitch", [-12.0, 0.0, 12.0]),
            ("semitones", [-12.0, 0.0, 12.0]),
            ("formant", [50.0, 100.0, 150.0]),
            ("Formant", [50.0, 100.0, 150.0]),
            ("mix", [0.0, 50.0, 100.0]),
            ("Mix", [0.0, 50.0, 100.0]),
            ("wet", [0.0, 50.0, 100.0]),
            ("dry", [0.0, 50.0, 100.0]),
            
            # 通用参数
            ("param_0", [0.0, 0.5, 1.0]),
            ("param_1", [0.0, 0.5, 1.0]),
            ("param_2", [0.0, 0.5, 1.0]),
            ("parameter_0", [0.0, 0.5, 1.0]),
            ("parameter_1", [0.0, 0.5, 1.0]),
            ("parameter_2", [0.0, 0.5, 1.0]),
            
            # 数字参数
            ("0", [0.0, 0.5, 1.0]),
            ("1", [0.0, 0.5, 1.0]),
            ("2", [0.0, 0.5, 1.0]),
        ]
        
        for param_name, test_values in common_params:
            self.log(f"🧪 测试参数: {param_name}")
            
            for test_value in test_values:
                try:
                    response = self.send_request("SetSourceFilterSettings", {
                        "sourceName": source_name,
                        "filterName": filter_name,
                        "filterSettings": {
                            param_name: test_value
                        }
                    }, timeout=2)
                    
                    if response and response.get('requestStatus', {}).get('result'):
                        self.log(f"  ✅ {param_name} = {test_value} 设置成功")
                        
                        # 验证设置
                        time.sleep(0.5)
                        verify_response = self.send_request("GetSourceFilter", {
                            "sourceName": source_name,
                            "filterName": filter_name
                        }, timeout=2)
                        
                        if verify_response and verify_response.get('requestStatus', {}).get('result'):
                            verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                            if param_name in verify_settings:
                                actual_value = verify_settings[param_name]
                                self.log(f"  📊 验证值: {actual_value}")
                                if abs(float(actual_value) - test_value) < 0.01:
                                    self.log(f"  🎯 参数 {param_name} 可能有效！")
                            else:
                                self.log(f"  ⚠️ 参数 {param_name} 未在设置中找到")
                    else:
                        self.log(f"  ❌ {param_name} = {test_value} 设置失败")
                        
                except Exception as e:
                    self.log(f"  ❌ 测试 {param_name} 时出错: {e}")
                    
            self.log("")  # 空行分隔
            
        self.log("🔍 方法1探索完成")
        self.log("=" * 50)
        
    def explore_direct_settings(self):
        """方法2: 尝试直接设置各种参数格式"""
        self.log("🔍 方法2: 尝试直接参数设置")
        self.log("=" * 50)
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        # 不同的参数设置格式
        test_settings = [
            {"pitch_shift": 3.0},
            {"pitch_bend": 0.5},
            {"transpose": 2},
            {"tune": 50.0},
            {"coarse": 2},
            {"fine": 10},
            {"voice_pitch": 1.2},
            {"formant_shift": 1.1},
            {"preserve_formants": True},
            {"correction_strength": 0.8},
            {"reference_frequency": 440.0},
            {"scale_type": 0},
            {"key": 0},
            {"bypass": False},
            {"enabled": True},
        ]
        
        for settings in test_settings:
            param_name = list(settings.keys())[0]
            param_value = list(settings.values())[0]
            
            self.log(f"🧪 测试设置: {param_name} = {param_value}")
            
            try:
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": settings
                }, timeout=2)
                
                if response and response.get('requestStatus', {}).get('result'):
                    self.log(f"  ✅ 设置成功")
                    
                    # 验证设置
                    time.sleep(0.5)
                    verify_response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=2)
                    
                    if verify_response and verify_response.get('requestStatus', {}).get('result'):
                        verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                        if param_name in verify_settings:
                            actual_value = verify_settings[param_name]
                            self.log(f"  📊 验证值: {actual_value}")
                            self.log(f"  🎯 参数 {param_name} 可能有效！")
                        else:
                            self.log(f"  ⚠️ 参数未在设置中找到")
                else:
                    self.log(f"  ❌ 设置失败")
                    
            except Exception as e:
                self.log(f"  ❌ 测试时出错: {e}")
                
        self.log("🔍 方法2探索完成")
        self.log("=" * 50)
        
    def explore_named_params(self):
        """方法3: 探索命名参数"""
        self.log("🔍 方法3: 探索Graillon特定参数名称")
        self.log("=" * 50)
        
        # 基于Graillon插件的可能参数名称
        graillon_params = [
            "pitch_correction",
            "pitch_shift_amount", 
            "formant_correction",
            "formant_shift_amount",
            "mix_amount",
            "dry_wet_mix",
            "correction_speed",
            "correction_smooth",
            "voice_character",
            "gender_shift",
            "throat_length",
            "mouth_size",
            "vibrato_rate",
            "vibrato_depth",
            "auto_tune_strength",
            "scale_correction",
            "note_transition",
            "humanize",
        ]
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        for param_name in graillon_params:
            self.log(f"🧪 测试Graillon参数: {param_name}")
            
            # 测试不同的数值
            test_values = [0.0, 0.5, 1.0, -0.5, 2.0, 100.0]
            
            for test_value in test_values:
                try:
                    response = self.send_request("SetSourceFilterSettings", {
                        "sourceName": source_name,
                        "filterName": filter_name,
                        "filterSettings": {
                            param_name: test_value
                        }
                    }, timeout=1)
                    
                    if response and response.get('requestStatus', {}).get('result'):
                        self.log(f"  ✅ {param_name} = {test_value} 可能有效")
                        break  # 如果成功就不测试其他值了
                        
                except Exception as e:
                    continue
                    
        self.log("🔍 方法3探索完成")
        self.log("=" * 50)
        
    def explore_filter_recreation(self):
        """方法4: 尝试重新创建滤镜"""
        self.log("🔍 方法4: 尝试重新创建VST滤镜")
        self.log("=" * 50)
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        # 首先获取当前滤镜信息
        self.log("📊 获取当前滤镜信息...")
        
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            filter_kind = filter_data.get('filterKind', '')
            current_settings = filter_data.get('filterSettings', {})
            
            self.log(f"当前滤镜类型: {filter_kind}")
            self.log(f"当前设置: {json.dumps(current_settings, indent=2)}")
            
            # 尝试删除并重新创建滤镜
            self.log("🗑️ 尝试删除当前滤镜...")
            
            delete_response = self.send_request("RemoveSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })
            
            if delete_response and delete_response.get('requestStatus', {}).get('result'):
                self.log("✅ 滤镜删除成功")
                
                # 等待一下
                time.sleep(1)
                
                # 重新创建滤镜，但使用修改过的设置
                self.log("🔧 重新创建滤镜...")
                
                # 修改设置，添加可能的参数
                new_settings = current_settings.copy()
                new_settings.update({
                    "pitch": 3.0,  # 尝试添加音调参数
                    "formant": 110.0,  # 尝试添加共振峰参数
                    "mix": 80.0,  # 尝试添加混合参数
                })
                
                create_response = self.send_request("CreateSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterKind": filter_kind,
                    "filterSettings": new_settings
                })
                
                if create_response and create_response.get('requestStatus', {}).get('result'):
                    self.log("✅ 滤镜重新创建成功")
                    self.log("🎯 请检查VST插件是否有变化")
                else:
                    self.log("❌ 滤镜重新创建失败")
                    
                    # 尝试恢复原始滤镜
                    self.log("🔄 尝试恢复原始滤镜...")
                    restore_response = self.send_request("CreateSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name,
                        "filterKind": filter_kind,
                        "filterSettings": current_settings
                    })
                    
                    if restore_response and restore_response.get('requestStatus', {}).get('result'):
                        self.log("✅ 原始滤镜已恢复")
                    else:
                        self.log("❌ 恢复原始滤镜失败！请手动在OBS中重新添加")
            else:
                self.log("❌ 删除滤镜失败")
        else:
            self.log("❌ 获取滤镜信息失败")
            
        self.log("🔍 方法4探索完成")
        self.log("=" * 50)
        
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTParameterExplorer()
    app.run()
