#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ Graillon真实参数测试器
基于OBS API探测到的真实参数进行精确测试
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class GraillonRealTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ Graillon真实参数测试器")
        self.root.geometry("1200x800")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0

        # 真实发现的参数
        self.real_params = {
            "coarse": 5.0,
            "fine": 5.0, 
            "pitch": 5.0,
            "pitch_shift": 5.0,
            "pitch_shift_amount": 5.0,
            "transpose": 5.0,
            "tune": 5.0
        }

        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST控制区域
        vst_frame = ttk.LabelFrame(main_frame, text="🎛️ VST滤镜控制", padding="10")
        vst_frame.pack(fill=tk.X, pady=(0, 10))
        
        vst_row = ttk.Frame(vst_frame)
        vst_row.pack(fill=tk.X)
        
        ttk.Label(vst_row, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(vst_row, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(vst_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(vst_row, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        self.enable_btn = ttk.Button(vst_row, text="✅ 启用VST滤镜",
                                    command=self.enable_vst_filter, state="disabled")
        self.enable_btn.pack(side=tk.LEFT, padx=(10, 5))
        
        self.check_status_btn = ttk.Button(vst_row, text="📊 检查状态",
                                          command=self.check_vst_status, state="disabled")
        self.check_status_btn.pack(side=tk.LEFT)
        
        # 参数测试区域
        test_frame = ttk.LabelFrame(main_frame, text="🧪 真实参数测试", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：单个参数测试
        row1 = ttk.Frame(test_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="测试参数:").pack(side=tk.LEFT)
        self.param_combo = ttk.Combobox(row1, values=list(self.real_params.keys()), 
                                       state="readonly", width=15)
        self.param_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.param_combo.set("pitch_shift")
        
        ttk.Label(row1, text="测试值:").pack(side=tk.LEFT)
        self.test_value_var = tk.StringVar(value="12.0")
        ttk.Entry(row1, textvariable=self.test_value_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        self.test_single_btn = ttk.Button(row1, text="🎯 测试参数", 
                                         command=self.test_single_parameter, state="disabled")
        self.test_single_btn.pack(side=tk.LEFT, padx=(10, 5))
        
        self.reset_btn = ttk.Button(row1, text="🔄 重置为5.0", 
                                   command=self.reset_parameter, state="disabled")
        self.reset_btn.pack(side=tk.LEFT)
        
        # 第二行：快速测试
        row2 = ttk.Frame(test_frame)
        row2.pack(fill=tk.X)
        
        self.test_pitch_btn = ttk.Button(row2, text="🎵 测试音调参数", 
                                        command=self.test_pitch_parameters, state="disabled")
        self.test_pitch_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.extreme_test_btn = ttk.Button(row2, text="⚡ 极端值测试",
                                          command=self.test_extreme_values, state="disabled")
        self.extreme_test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.restore_all_btn = ttk.Button(row2, text="🏠 恢复所有为5.0",
                                         command=self.restore_all_defaults, state="disabled")
        self.restore_all_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🎛️ Graillon真实参数测试器启动")
        self.log("基于OBS API探测到的真实Graillon 3参数进行测试")
        self.log("=" * 60)
        self.log("🎯 发现的真实参数:")
        for param, value in self.real_params.items():
            self.log(f"   {param}: {value}")
        self.log("=" * 60)
        self.log("⚠️ 重要: 请确保VST滤镜已启用才能看到效果")
        self.log("📋 使用步骤:")
        self.log("1. 连接OBS")
        self.log("2. 点击'启用VST滤镜'")
        self.log("3. 开始测试真实参数")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有按钮
        buttons = [self.enable_btn, self.check_status_btn, self.test_single_btn,
                  self.reset_btn, self.test_pitch_btn, self.extreme_test_btn,
                  self.restore_all_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🎉 所有测试功能已启用！")
        self.log("💡 建议先点击'启用VST滤镜'确保滤镜处于启用状态")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.enable_btn, self.check_status_btn, self.test_single_btn,
                  self.reset_btn, self.test_pitch_btn, self.extreme_test_btn,
                  self.restore_all_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None

    def enable_vst_filter(self):
        """启用VST滤镜"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("✅ 启用VST滤镜...")

        response = self.send_request("SetSourceFilterEnabled", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterEnabled": True
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ VST滤镜已启用！")
            self.log("🎧 现在应该可以听到音频效果了")

            # 验证启用状态
            time.sleep(0.5)
            self.check_vst_status()
        else:
            self.log("❌ 启用VST滤镜失败")

    def check_vst_status(self):
        """检查VST滤镜状态"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("📊 检查VST滤镜状态...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            enabled = filter_data.get('filterEnabled', False)
            settings = filter_data.get('filterSettings', {})

            status_icon = "✅" if enabled else "❌"
            self.log(f"{status_icon} VST滤镜状态: {'启用' if enabled else '禁用'}")

            self.log("📊 当前参数值:")
            param_found = False
            for param_name in self.real_params.keys():
                if param_name in settings:
                    current_value = settings[param_name]
                    self.log(f"   {param_name}: {current_value}")
                    # 更新本地记录
                    self.real_params[param_name] = current_value
                    param_found = True
                else:
                    self.log(f"   {param_name}: 未找到")

            if not param_found:
                self.log("   ⚠️ 未找到任何预期参数，显示所有参数:")
                for key, value in settings.items():
                    if key not in ['plugin_path', 'chunk_data', 'chunk_hash']:
                        self.log(f"   {key}: {value}")
        else:
            self.log("❌ 获取VST滤镜状态失败")

    def test_single_parameter(self):
        """测试单个参数"""
        param_name = self.param_combo.get()
        try:
            test_value = float(self.test_value_var.get())
        except ValueError:
            messagebox.showerror("错误", "测试值必须是数字")
            return

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        original_value = self.real_params.get(param_name, 5.0)

        self.log(f"🎯 测试真实参数: {param_name}")
        self.log(f"   原始值: {original_value}")
        self.log(f"   测试值: {test_value}")

        # 设置参数
        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: test_value
            }
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 参数设置成功")

            # 验证设置
            time.sleep(0.5)
            verify_response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if verify_response and verify_response.get('requestStatus', {}).get('result'):
                verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                if param_name in verify_settings:
                    actual_value = verify_settings[param_name]
                    self.log(f"📊 验证值: {actual_value}")

                    try:
                        if abs(float(actual_value) - test_value) < 0.01:
                            self.log("🎉 参数设置已确认生效！")
                            self.log("🎧 请仔细听音频变化...")

                            # 更新本地记录
                            self.real_params[param_name] = float(actual_value)

                            if abs(test_value - original_value) > 1:
                                self.log("💡 如果听到了音调变化，说明这个参数有效！")
                        else:
                            self.log(f"⚠️ 设置值与验证值不匹配 (期望: {test_value}, 实际: {actual_value})")
                    except (ValueError, TypeError):
                        self.log(f"📝 参数值已更新为: {actual_value}")
                else:
                    self.log("❌ 验证时未找到参数")
            else:
                self.log("❌ 验证请求失败")
        else:
            self.log("❌ 参数设置失败")

    def reset_parameter(self):
        """重置参数到5.0"""
        param_name = self.param_combo.get()

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log(f"🔄 重置参数: {param_name} = 5.0")

        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: 5.0
            }
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 参数已重置为5.0")
            self.real_params[param_name] = 5.0
        else:
            self.log("❌ 参数重置失败")

    def test_pitch_parameters(self):
        """测试所有音调参数"""
        self.log("🎵 开始测试所有音调参数...")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 音调测试序列：从低到高
        test_sequence = [
            ("pitch_shift", -12.0, "降低一个八度"),
            ("pitch_shift", 0.0, "恢复正常"),
            ("pitch_shift", 12.0, "升高一个八度"),
            ("pitch_shift", 5.0, "恢复原始值"),

            ("transpose", -12.0, "移调降低一个八度"),
            ("transpose", 12.0, "移调升高一个八度"),
            ("transpose", 5.0, "恢复原始值"),

            ("pitch", -12.0, "音调降低一个八度"),
            ("pitch", 12.0, "音调升高一个八度"),
            ("pitch", 5.0, "恢复原始值"),
        ]

        for param_name, test_value, description in test_sequence:
            self.log(f"\n🎵 {description}: {param_name} = {test_value}")

            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: test_value
                }
            }, timeout=3)

            if response and response.get('requestStatus', {}).get('result'):
                self.log("  ✅ 设置成功")
                self.log("  🎧 请仔细听音频变化...")
                time.sleep(3)  # 给用户3秒时间听效果
            else:
                self.log("  ❌ 设置失败")

        self.log("\n🎵 音调参数测试完成！")
        self.log("🎧 如果听到了音调变化，说明对应的参数是有效的")

    def test_extreme_values(self):
        """测试极端值"""
        self.log("⚡ 开始极端值测试...")
        self.log("使用极端数值，应该能听到明显变化")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 极端值测试
        extreme_tests = [
            ("pitch_shift", -24.0, "音调极低"),
            ("pitch_shift", 24.0, "音调极高"),
            ("pitch_shift", 5.0, "恢复"),

            ("transpose", -24.0, "移调极低"),
            ("transpose", 24.0, "移调极高"),
            ("transpose", 5.0, "恢复"),

            ("coarse", 0.0, "粗调最小"),
            ("coarse", 10.0, "粗调最大"),
            ("coarse", 5.0, "恢复"),
        ]

        for param_name, test_value, description in extreme_tests:
            self.log(f"\n⚡ {description}: {param_name} = {test_value}")

            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: test_value
                }
            }, timeout=3)

            if response and response.get('requestStatus', {}).get('result'):
                self.log("  ✅ 设置成功")
                self.log("  🎧 请仔细听音频变化...")
                time.sleep(2)
            else:
                self.log("  ❌ 设置失败")

        self.log("\n⚡ 极端值测试完成！")

    def restore_all_defaults(self):
        """恢复所有参数为5.0"""
        self.log("🏠 恢复所有参数为默认值5.0...")

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        default_settings = {param: 5.0 for param in self.real_params.keys()}

        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": default_settings
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 所有参数已恢复为5.0")
            # 更新本地记录
            for param in self.real_params.keys():
                self.real_params[param] = 5.0
        else:
            self.log("❌ 恢复默认值失败")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GraillonRealTester()
    app.run()
