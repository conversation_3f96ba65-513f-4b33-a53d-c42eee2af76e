# 🎛️ 真实VST插件ID检测器使用说明

## 📋 工具概述

这是一个真实连接到OBS WebSocket的VST插件ID检测器，可以获取你实际安装的VST2x插件的真实ID和参数信息。

### 🎯 主要功能
- 🔗 真实连接OBS WebSocket
- 🎤 自动检测音频源
- 🎛️ 识别VST2x滤镜插件
- 📊 获取插件ID和所有参数信息
- 💾 导出检测结果为JSON文件

## 🚀 快速开始

### 1. 环境准备

#### 安装依赖包
```bash
pip install websocket-client
```

#### 确保OBS设置正确
1. **启动OBS Studio**
2. **启用WebSocket服务器**：
   - 工具 → WebSocket服务器设置
   - 勾选"启用WebSocket服务器"
   - 服务器端口：4455（默认）
   - 建议取消勾选"启用身份验证"（简化连接）

### 2. 启动检测器

```bash
# 方法1：直接运行
python real_vst_plugin_detector.py

# 方法2：使用启动脚本
python run_real_vst_detector.py
```

### 3. 连接OBS

1. **检查WebSocket地址**：默认为 `ws://localhost:4455`
2. **点击"🔗 连接OBS"按钮**
3. **等待连接成功提示**：显示"✅ 已连接到OBS"

## 🎛️ 检测你的三个VST插件

### 步骤1：选择音频源
在左侧"音频源选择"区域，选择包含VST插件的音频源（通常是"麦克风"）。

### 步骤2：检测VST滤镜
- 选择音频源后会自动检测滤镜
- 或点击"🔍 检测滤镜"按钮手动检测
- VST滤镜会在列表中显示

### 步骤3：分析VST插件
1. **选择VST滤镜**：在滤镜列表中点击你的VST插件
2. **点击"📊 分析VST"按钮**
3. **查看详细信息**：右侧会显示插件的完整信息

## 📊 获取插件信息

### 🔍 VST插件信息标签页
显示你的VST插件的基本信息：

#### Auburn Sounds Graillon 3-64 示例
```
滤镜名称: Graillon音调
插件ID: vst_filter (或具体的插件标识)
滤镜类型: vst_filter
启用状态: ✅ 启用
插件路径: C:/Program Files/VstPlugins/Auburn Sounds/Graillon 3-64.dll
```

#### TSE_808_2.0_x64 示例
```
滤镜名称: TSE808失真
插件ID: vst_filter
滤镜类型: vst_filter
启用状态: ✅ 启用
插件路径: C:/Program Files/VstPlugins/TSE/TSE_808_2.0_x64.dll
```

#### TAL-Reverb-4-64 示例
```
滤镜名称: TAL混响
插件ID: vst_filter
滤镜类型: vst_filter
启用状态: ✅ 启用
插件路径: C:/Program Files/VstPlugins/TAL/TAL-Reverb-4-64.dll
```

### 📊 参数列表标签页
显示每个VST插件的所有参数：

#### 参数信息包含
- **参数名称**：如 `pitch`, `drive`, `roomsize` 等
- **当前值**：参数的实时数值
- **参数类型**：float, bool, str 等
- **参数描述**：功能说明

#### 常见参数示例
```
Graillon 3-64:
- pitch: 0.0 (float) - 音调调节参数
- formant: 100.0 (float) - 共振峰调节
- mix: 100.0 (float) - 干湿混合比例

TSE 808:
- drive: 30.0 (float) - 驱动/失真强度
- tone: 50.0 (float) - 音色调节参数
- level: 80.0 (float) - 音量/增益参数

TAL Reverb:
- roomsize: 40.0 (float) - 混响相关参数
- damping: 60.0 (float) - 混响相关参数
- mix: 25.0 (float) - 干湿混合比例
```

### 📄 原始数据标签页
显示从OBS获取的完整JSON数据，包含所有技术细节。

## 🚀 一键检测所有VST

点击"🚀 一键检测所有VST插件"按钮，工具会：
1. 扫描所有音频源
2. 检测每个源的VST滤镜
3. 获取所有VST插件的详细信息
4. 生成完整的检测报告

## 💾 导出检测结果

### 导出功能
1. **点击"💾 导出"按钮**
2. **选择保存位置**
3. **文件自动命名**：`VST插件检测结果_20250728_153045.json`

### 导出文件格式
```json
{
  "timestamp": "2025-07-28 15:30:45",
  "detector_version": "1.0",
  "obs_connection": {
    "url": "ws://localhost:4455",
    "connected": true
  },
  "detection_results": {
    "麦克风": {
      "Graillon音调": {
        "filterName": "Graillon音调",
        "filterKind": "vst_filter",
        "filterEnabled": true,
        "filterSettings": {
          "pitch": 0.0,
          "formant": 100.0,
          "mix": 100.0,
          "plugin_path": "C:/Program Files/VstPlugins/Auburn Sounds/Graillon 3-64.dll"
        }
      },
      "TSE808失真": {
        "filterName": "TSE808失真",
        "filterKind": "vst_filter",
        "filterEnabled": true,
        "filterSettings": {
          "drive": 30.0,
          "tone": 50.0,
          "level": 80.0
        }
      }
    }
  }
}
```

## 🔧 高级功能

### 参数过滤
- **全部参数**：显示所有参数
- **数值参数**：只显示数值类型参数
- **布尔参数**：只显示开关类型参数
- **字符串参数**：只显示文本类型参数

### 实时刷新
- **🔄 刷新参数**：重新获取最新的参数值
- **🔄 刷新源列表**：重新扫描音频源

### 日志功能
- **查看详细日志**：所有操作都有详细记录
- **保存日志**：可导出日志文件用于调试
- **自动滚动**：新日志自动显示

## ⚠️ 注意事项

### 使用前检查
1. **OBS必须正在运行**
2. **WebSocket服务器必须启用**
3. **VST插件必须已添加到音频源**
4. **确保网络连接正常**

### 常见问题解决

#### 连接失败
- 检查OBS是否运行
- 检查WebSocket端口是否正确（默认4455）
- 检查防火墙设置

#### 检测不到VST插件
- 确认VST插件已正确添加到音频源
- 检查滤镜是否启用
- 尝试刷新滤镜列表

#### 参数显示异常
- 点击"🔄 刷新参数"按钮
- 检查VST插件是否正常工作
- 查看日志获取详细错误信息

## 🎯 实际应用

使用这个工具获取的真实插件信息，你可以：

1. **精确控制VST参数**：在主程序中使用正确的参数名称
2. **创建自动化脚本**：基于真实的参数范围
3. **调试VST问题**：查看参数的实时变化
4. **生成配置文件**：为其他工具提供准确的插件信息

## 📞 技术支持

如果遇到问题：
1. 查看日志标签页的详细信息
2. 检查OBS和VST插件设置
3. 确认网络连接和端口设置
4. 导出日志文件用于问题分析
