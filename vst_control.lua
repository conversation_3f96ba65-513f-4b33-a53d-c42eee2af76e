-- OBS VST插件控制脚本
obs = obslua

-- 配置参数
local settings = {
    source_name = "",
    filter_name = "",
    active = false,
    interval = 3.0,
    debug = true
}

local last_update = 0

-- 日志函数
local function log(message, level)
    level = level or obs.LOG_INFO
    if settings.debug then
        obs.script_log(level, "[VST控制] " .. tostring(message))
    end
end

-- 通用VST参数控制函数
local function set_vst_parameter(source_name, filter_name, param_name, value)
    local source = obs.obs_get_source_by_name(source_name)
    if not source then
        log("音频源不存在: " .. tostring(source_name), obs.LOG_WARNING)
        return false
    end

    local filter = obs.obs_source_get_filter_by_name(source, filter_name)
    if not filter then
        log("VST滤镜不存在: " .. tostring(filter_name), obs.LOG_WARNING)
        obs.obs_source_release(source)
        return false
    end

    -- 获取滤镜设置
    local filter_settings = obs.obs_source_get_settings(filter)
    
    -- 设置参数 - 尝试不同的数据类型
    obs.obs_data_set_double(filter_settings, param_name, value)
    obs.obs_data_set_int(filter_settings, param_name, math.floor(value))
    obs.obs_data_set_string(filter_settings, param_name, tostring(value))
    
    -- 应用设置
    obs.obs_source_update(filter, filter_settings)
    
    log(string.format("设置参数 %s.%s = %.2f", filter_name, param_name, value))
    
    -- 清理资源
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    
    return true
end

-- Graillon音调控制
local function control_graillon()
    if not settings.source_name or settings.source_name == "" then
        return
    end
    
    -- 随机音调值 (-6到6半音)
    local pitch = math.random() * 12 - 6
    
    -- 常见的Graillon参数名称
    local param_names = {"pitch", "Pitch", "PITCH", "correction", "Correction"}
    
    for _, param_name in ipairs(param_names) do
        set_vst_parameter(settings.source_name, settings.filter_name, param_name, pitch)
    end
    
    log(string.format("Graillon音调设置为: %.1f半音", pitch))
end

-- TSE808失真控制
local function control_tse808()
    if not settings.source_name or settings.source_name == "" then
        return
    end
    
    -- 随机失真参数
    local drive = math.random() * 80 + 20  -- 20-100%
    local tone = math.random() * 60 + 20   -- 20-80%
    
    -- TSE808参数名称
    local drive_names = {"drive", "Drive", "DRIVE", "gain", "Gain"}
    local tone_names = {"tone", "Tone", "TONE", "eq", "EQ"}
    
    for _, param_name in ipairs(drive_names) do
        set_vst_parameter(settings.source_name, settings.filter_name, param_name, drive/100)
    end
    
    for _, param_name in ipairs(tone_names) do
        set_vst_parameter(settings.source_name, settings.filter_name, param_name, tone/100)
    end
    
    log(string.format("TSE808设置 - 强度: %.1f%%, 音色: %.1f%%", drive, tone))
end

-- TAL混响控制
local function control_tal_reverb()
    if not settings.source_name or settings.source_name == "" then
        return
    end
    
    -- 随机混响参数
    local roomsize = math.random() * 60 + 20  -- 20-80%
    local mix = math.random() * 40 + 10       -- 10-50%
    
    -- TAL-Reverb参数名称
    local roomsize_names = {"roomsize", "RoomSize", "ROOMSIZE", "size", "Size"}
    local mix_names = {"mix", "Mix", "MIX", "wet", "Wet"}
    
    for _, param_name in ipairs(roomsize_names) do
        set_vst_parameter(settings.source_name, settings.filter_name, param_name, roomsize/100)
    end
    
    for _, param_name in ipairs(mix_names) do
        set_vst_parameter(settings.source_name, settings.filter_name, param_name, mix/100)
    end
    
    log(string.format("TAL混响设置 - 房间: %.1f%%, 混合: %.1f%%", roomsize, mix))
end

-- 获取音频源列表
local function get_audio_sources()
    local sources = {}
    local all_sources = obs.obs_enum_sources()
    if all_sources then
        for _, src in ipairs(all_sources) do
            local name = obs.obs_source_get_name(src)
            table.insert(sources, name)
            obs.obs_source_release(src)
        end
    end
    return sources
end

-- 脚本属性界面
function script_properties()
    local props = obs.obs_properties_create()
    
    -- 音频源选择
    local source_list = obs.obs_properties_add_list(props, "source_name", "选择音频源", 
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)
    
    for _, name in ipairs(get_audio_sources()) do
        obs.obs_property_list_add_string(source_list, name, name)
    end
    
    -- VST滤镜名称
    obs.obs_properties_add_text(props, "filter_name", "VST滤镜名称", obs.OBS_TEXT_DEFAULT)
    
    -- 更新间隔
    obs.obs_properties_add_float(props, "interval", "更新间隔(秒)", 0.5, 30.0, 0.1)
    
    -- 控制开关
    obs.obs_properties_add_bool(props, "active", "启用VST自动控制")
    
    -- 调试开关
    obs.obs_properties_add_bool(props, "debug", "启用调试日志")
    
    return props
end

-- 更新设置
function script_update(new_settings)
    settings.source_name = obs.obs_data_get_string(new_settings, "source_name")
    settings.filter_name = obs.obs_data_get_string(new_settings, "filter_name")
    settings.interval = obs.obs_data_get_double(new_settings, "interval")
    settings.active = obs.obs_data_get_bool(new_settings, "active")
    settings.debug = obs.obs_data_get_bool(new_settings, "debug")
    
    log("设置已更新")
end

-- 主循环
function script_tick(seconds)
    if settings.active and os.clock() >= last_update then
        -- 根据滤镜名称判断插件类型
        local filter_lower = string.lower(settings.filter_name or "")
        
        if string.find(filter_lower, "graillon") then
            control_graillon()
        elseif string.find(filter_lower, "tse") or string.find(filter_lower, "808") then
            control_tse808()
        elseif string.find(filter_lower, "tal") or string.find(filter_lower, "reverb") then
            control_tal_reverb()
        else
            -- 通用控制 - 随机设置一些常见参数
            local value = math.random()
            set_vst_parameter(settings.source_name, settings.filter_name, "param1", value)
        end
        
        -- 设置下次更新时间
        last_update = os.clock() + settings.interval
    end
end

-- 脚本描述
function script_description()
    return [[🎛️ VST插件自动控制器

功能特点：
✅ 支持Graillon音调控制
✅ 支持TSE808失真控制  
✅ 支持TAL混响控制
✅ 自动识别插件类型
✅ 可调节更新间隔
✅ 详细调试日志

使用方法：
1. 选择包含VST滤镜的音频源
2. 输入VST滤镜的确切名称
3. 设置更新间隔时间
4. 启用自动控制

注意事项：
- 确保VST插件已正确加载
- 滤镜名称必须完全匹配
- 建议先启用调试查看日志]]
end
