#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 快速音调测试
基于您的日志快速测试最有希望的参数
"""

import json
import websocket
import time

def send_request(ws, request_type, request_data=None):
    """发送请求"""
    request_id = f"req-{int(time.time())}"
    
    payload = {
        "op": 6,
        "d": {
            "requestType": request_type,
            "requestId": request_id,
            "requestData": request_data or {}
        }
    }
    
    try:
        ws.send(json.dumps(payload))
        
        start_time = time.time()
        while time.time() - start_time < 5:
            try:
                response_raw = ws.recv()
                response_data = json.loads(response_raw)
                
                if (response_data.get("op") == 7 and 
                    response_data.get("d", {}).get("requestId") == request_id):
                    return response_data.get("d", {})
                    
            except:
                continue
                
        return None
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_pitch_param(ws, param_name, test_values, source_name="媒体源", filter_name="VST 2.x 插件"):
    """测试单个音调参数"""
    print(f"\n🧪 测试参数: {param_name}")
    
    for test_value in test_values:
        print(f"   🎵 设置为: {test_value}")
        
        response = send_request(ws, "SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: test_value
            }
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            print(f"      ✅ 设置成功，请听音调变化...")
            time.sleep(2)  # 等待2秒听音调变化
        else:
            print(f"      ❌ 设置失败")
            
    print(f"   🔄 测试完成")

def main():
    print("🚀 快速音调参数测试")
    print("=" * 50)
    
    # 连接OBS
    try:
        ws = websocket.create_connection("ws://localhost:4455", timeout=5)
        
        # 接收Hello消息
        hello_raw = ws.recv()
        hello_data = json.loads(hello_raw)
        
        # 发送Identify消息
        identify_payload = {
            "op": 1,
            "d": {
                "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                "eventSubscriptions": 33
            }
        }
        ws.send(json.dumps(identify_payload))
        
        # 接收Identified消息
        identified_raw = ws.recv()
        identified_data = json.loads(identified_raw)
        
        print("✅ 成功连接到OBS")
        
    except Exception as e:
        print(f"❌ 连接OBS失败: {e}")
        return
    
    print("\n💡 请确保OBS中有音频播放，然后仔细听音调变化")
    print("=" * 50)
    
    # 基于您的日志测试最有希望的参数
    test_cases = [
        # 参数名, 测试值列表
        ("pitch", [-5.0, 0.0, 5.0]),
        ("pitch_shift_amount", [-12.0, 0.0, 12.0]),
        ("coarse", [-5, 0, 5]),
        ("transpose", [-5, 0, 5]),
        ("pitch_shift", [0.0, 5.0, 10.0]),
        ("fine", [-20, 0, 20]),
        ("tune", [25.0, 50.0, 75.0]),
    ]
    
    for param_name, test_values in test_cases:
        test_pitch_param(ws, param_name, test_values)
        
        # 询问用户是否听到了音调变化
        user_input = input(f"\n❓ 参数 '{param_name}' 是否产生了音调变化？(y/n/q退出): ").lower()
        
        if user_input == 'y':
            print(f"🎯 找到音调参数: {param_name}")
            print(f"💡 这个参数可以控制音调！")
            
            # 演示音阶
            print(f"🎭 使用 '{param_name}' 演示音阶:")
            scale_values = [-12, -10, -8, -5, -3, -1, 0, 2, 4, 5, 7, 9, 11, 12]
            
            for value in scale_values:
                send_request(ws, "SetSourceFilterSettings", {
                    "sourceName": "媒体源",
                    "filterName": "VST 2.x 插件",
                    "filterSettings": {
                        param_name: value
                    }
                })
                time.sleep(0.5)
                
            # 重置为0
            send_request(ws, "SetSourceFilterSettings", {
                "sourceName": "媒体源",
                "filterName": "VST 2.x 插件",
                "filterSettings": {
                    param_name: 0
                }
            })
            
            break
            
        elif user_input == 'q':
            break
        else:
            print(f"   ℹ️ 继续测试下一个参数")
    
    # 重置所有参数
    print("\n🔄 重置所有参数...")
    reset_settings = {
        'pitch': 0.0,
        'pitch_shift_amount': 0.0,
        'coarse': 0,
        'transpose': 0,
        'pitch_shift': 0.0,
        'fine': 0,
        'tune': 50.0,
    }
    
    send_request(ws, "SetSourceFilterSettings", {
        "sourceName": "媒体源",
        "filterName": "VST 2.x 插件",
        "filterSettings": reset_settings
    })
    
    print("✅ 测试完成！")
    ws.close()

if __name__ == "__main__":
    main()
