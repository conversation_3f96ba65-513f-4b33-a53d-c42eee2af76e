#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 VST插件检测器测试版本
简化版本，用于测试基本功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import websocket
import threading
import time
from datetime import datetime

class TestVSTDetector:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 VST插件检测器 - 测试版")
        self.root.geometry("800x600")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 连接状态
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        # 连接按钮
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # WebSocket地址
        self.ws_url = tk.StringVar(value="ws://localhost:4455")
        ttk.Label(conn_frame, text="地址:").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Entry(conn_frame, textvariable=self.ws_url, width=20).pack(side=tk.LEFT)
        
        # 操作区域
        op_frame = ttk.LabelFrame(main_frame, text="🎛️ VST检测操作", padding="10")
        op_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 音频源选择
        ttk.Label(op_frame, text="音频源:").pack(side=tk.LEFT)
        self.source_combo = ttk.Combobox(op_frame, width=20, state="readonly")
        self.source_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        # 操作按钮
        self.refresh_btn = ttk.Button(op_frame, text="🔄 刷新源", command=self.refresh_sources, state="disabled")
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.detect_btn = ttk.Button(op_frame, text="🔍 检测VST", command=self.detect_vst, state="disabled")
        self.detect_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.export_btn = ttk.Button(op_frame, text="💾 导出", command=self.export_results, state="disabled")
        self.export_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 检测结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框显示结果
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始化结果显示
        self.log("🧪 VST插件检测器测试版启动")
        self.log("请先连接到OBS WebSocket")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    # 创建WebSocket连接
                    self.ws = websocket.create_connection(self.ws_url.get(), timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    # 连接成功
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.refresh_btn.config(state="normal")
        self.detect_btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.refresh_sources()
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.refresh_btn.config(state="disabled")
        self.detect_btn.config(state="disabled")
        self.export_btn.config(state="disabled")
        
        self.source_combo.set('')
        self.source_combo['values'] = []
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
            
    def refresh_sources(self):
        """刷新音频源"""
        if not self.is_connected:
            return

        self.log("🔄 正在刷新音频源...")

        response = self.send_request("GetInputList")
        if response and response.get('requestStatus', {}).get('result'):
            inputs = response.get('responseData', {}).get('inputs', [])
            self.log(f"📋 获取到 {len(inputs)} 个输入源")

            # 使用主程序的音频源识别逻辑
            audio_sources = []
            for item in inputs:
                input_name = item.get("inputName")
                input_kind = item.get("inputKind")
                if not input_name:
                    continue

                # 计算各种标志位（参考主程序逻辑）
                has_audio_tracks = item.get("audioTracks") is not None and len(item.get("audioTracks")) > 0
                is_dedicated_audio = input_kind in [
                    "wasapi_input_capture", "wasapi_output_capture",
                    "coreaudio_input_capture", "coreaudio_output_capture",
                    "pulse_input_capture", "pulse_output_capture",
                    "jack_input_client", "alsa_input_capture"
                ]
                is_audio_capable = has_audio_tracks or is_dedicated_audio
                is_media_source = (input_kind == 'ffmpeg_source' or input_kind == 'vlc_source')

                # 填充音频媒体源（有音频能力的源 或 媒体源）
                if is_audio_capable or is_media_source:
                    display_name = f"{input_name} ({input_kind})"
                    audio_sources.append(input_name)
                    self.log(f"  🎤 音频源: {display_name}")

            self.source_combo['values'] = audio_sources
            if audio_sources:
                self.source_combo.set(audio_sources[0])
                self.log(f"✅ 找到 {len(audio_sources)} 个音频源")
            else:
                self.log("⚠️ 未找到音频源")
                self.log("💡 提示：请确保在OBS中添加了音频源（如麦克风、桌面音频等）")
        else:
            self.log("❌ 获取音频源失败")
            
    def detect_vst(self):
        """检测VST插件"""
        source_name = self.source_combo.get()
        if not source_name:
            messagebox.showwarning("警告", "请先选择音频源")
            return
            
        self.log(f"🔍 正在检测音频源 '{source_name}' 的VST插件...")
        
        # 获取滤镜列表
        response = self.send_request("GetSourceFilterList", {"sourceName": source_name})
        
        if response and response.get('requestStatus', {}).get('result'):
            filters = response.get('responseData', {}).get('filters', [])
            
            self.log(f"📋 找到 {len(filters)} 个滤镜")
            
            vst_filters = []
            for filter_item in filters:
                filter_name = filter_item.get('filterName', '')
                filter_kind = filter_item.get('filterKind', '')
                
                if 'vst' in filter_kind.lower():
                    vst_filters.append(filter_item)
                    self.log(f"  🎛️ VST滤镜: {filter_name} ({filter_kind})")
                    
                    # 获取详细信息
                    detail_response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    })
                    
                    if detail_response and detail_response.get('requestStatus', {}).get('result'):
                        filter_data = detail_response.get('responseData', {})
                        self.display_vst_info(filter_data)
                        
            if vst_filters:
                self.export_btn.config(state="normal")
                self.log(f"✅ 检测完成，找到 {len(vst_filters)} 个VST插件")
            else:
                self.log("⚠️ 未找到VST插件")
                
        else:
            self.log("❌ 获取滤镜列表失败")
            
    def display_vst_info(self, filter_data):
        """显示VST信息"""
        self.log("\n" + "="*50)
        self.log(f"🎛️ VST插件详细信息:")
        self.log(f"滤镜名称: {filter_data.get('filterName', 'Unknown')}")
        self.log(f"插件类型: {filter_data.get('filterKind', 'Unknown')}")
        self.log(f"启用状态: {'✅ 启用' if filter_data.get('filterEnabled') else '❌ 禁用'}")
        
        settings = filter_data.get('filterSettings', {})
        if settings:
            self.log(f"\n📊 插件参数 ({len(settings)}个):")
            for param_name, param_value in settings.items():
                param_type = type(param_value).__name__
                self.log(f"  • {param_name}: {param_value} ({param_type})")
                
        self.log("="*50 + "\n")
        
        # 存储数据用于导出
        if not hasattr(self, 'detected_data'):
            self.detected_data = {}
        source_name = self.source_combo.get()
        if source_name not in self.detected_data:
            self.detected_data[source_name] = {}
        self.detected_data[source_name][filter_data.get('filterName', 'Unknown')] = filter_data
        
    def export_results(self):
        """导出结果"""
        if not hasattr(self, 'detected_data'):
            messagebox.showwarning("警告", "没有可导出的数据")
            return
            
        from tkinter import filedialog
        
        file_name = filedialog.asksaveasfilename(
            title="导出VST检测结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json")],
            initialname=f"VST检测结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        if file_name:
            try:
                export_data = {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "detection_results": self.detected_data
                }
                
                with open(file_name, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=4, ensure_ascii=False)
                    
                self.log(f"💾 结果已导出到: {file_name}")
                messagebox.showinfo("导出成功", f"结果已导出到:\n{file_name}")
                
            except Exception as e:
                self.log(f"❌ 导出失败: {e}")
                messagebox.showerror("导出失败", f"导出时发生错误:\n{e}")
                
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = TestVSTDetector()
    app.run()
