# 🎵 VST音调参数检测完整指南

## 🚨 问题诊断

您遇到的问题：**监控功能无法检测到音调参数变化**

可能的原因：
1. **检测精度问题** - 原来的0.001精度可能太高
2. **参数名称不匹配** - 实际参数名可能不是"pitch"
3. **数值单位问题** - 您提到可能是dB单位
4. **VST插件特殊性** - 某些插件的参数更新机制特殊

## 🛠️ 解决方案

### 方案1: 使用专门的音调检测器

我为您创建了 `vst_pitch_detector.py`，这是专门用于找到音调参数的工具：

```bash
python vst_pitch_detector.py
```

**使用步骤：**
1. 连接OBS
2. 设置正确的音频源和VST滤镜名称
3. 点击"📊 记录基准状态"
4. 在OBS中调节音调滑块
5. 点击"🎵 检测音调变化"

### 方案2: 使用改进的实时监控

改进后的 `vst_parameter_tester.py` 现在具有：
- **超敏感检测**: 精度从0.001提升到0.0001
- **音调参数识别**: 自动标记可能的音调参数
- **详细变化日志**: 显示精确的数值变化

## 🔍 详细检测步骤

### 步骤1: 基础检查

```
1. 启动 vst_pitch_detector.py
2. 连接OBS
3. 点击"📋 显示所有参数"
4. 查看所有可用参数，寻找音调相关的名称
```

**常见音调参数名称：**
- `pitch`, `Pitch`, `PITCH`
- `tune`, `Tune`, `TUNE`
- `semitones`, `transpose`
- `coarse`, `fine`
- `frequency`, `freq`
- `cents`, `detune`, `shift`
- `param_0`, `param_1` (数字参数)

### 步骤2: 自动测试

```
1. 点击"🧪 测试常见音调参数"
2. 程序会自动测试所有可能的参数名称
3. 查看哪些参数可以成功设置
```

### 步骤3: 手动检测

```
1. 点击"📊 记录基准状态"
2. 在OBS中调节音调滑块（大幅度调节）
3. 点击"🎵 检测音调变化"
4. 查看哪些参数发生了变化
```

### 步骤4: 实时监控

```
1. 点击"📡 开始实时监控"
2. 在OBS中慢慢调节音调滑块
3. 观察程序中的实时变化日志
4. 寻找标记为"🎵 可能是音调参数"的项目
```

## 🎯 针对dB单位的特殊处理

如果您的VST插件使用dB单位，可能的参数值范围：
- **音调**: -24dB 到 +24dB (对应-24到+24半音)
- **增益**: -60dB 到 +20dB
- **滤波器**: 20Hz 到 20000Hz

**检测技巧：**
1. 大幅度调节音调（比如从0调到+12或-12）
2. 寻找变化范围在-50到+50之间的参数
3. 注意参数名称包含"pitch", "tune", "freq"等关键词

## 🔧 故障排除

### 问题1: 仍然检测不到变化

**解决方法：**
```
1. 确认OBS中VST插件界面确实有变化
2. 检查音频源名称是否完全正确
3. 检查VST滤镜名称是否完全正确
4. 尝试重启OBS和程序
```

### 问题2: 检测到变化但不是音调参数

**解决方法：**
```
1. 在OBS中播放音频
2. 手动调节检测到的参数
3. 听音频是否有音调变化
4. 记录确实影响音调的参数名称
```

### 问题3: 参数设置成功但没有音频效果

**解决方法：**
```
1. 检查VST插件是否正确加载
2. 确认音频源有声音输入
3. 检查OBS音频监控设置
4. 尝试在VST插件界面手动调节
```

## 💡 高级技巧

### 技巧1: 二进制搜索法

```
1. 记录基准状态
2. 将音调调到最大值，检测变化
3. 将音调调到最小值，检测变化
4. 找到变化最大的参数
```

### 技巧2: 参数名称推理

```
如果发现参数名称是 param_0, param_1 等：
1. 通常 param_0 是第一个参数（可能是音调）
2. 按顺序测试 param_0, param_1, param_2
3. 观察哪个参数影响音调
```

### 技巧3: 数值范围分析

```
音调参数的典型特征：
1. 数值范围通常在 -50 到 +50 之间
2. 默认值通常是 0 或接近 0
3. 调节时变化相对较大（>0.1）
```

## 📞 如果仍然无法解决

请提供以下信息：
1. VST插件的具体名称和版本
2. "显示所有参数"的完整输出
3. 在OBS中手动调节时的具体操作
4. 是否听到了音调变化

---

**记住：** 不同的VST插件可能使用完全不同的参数名称和数值范围。关键是要有耐心，系统性地测试所有可能的参数。
