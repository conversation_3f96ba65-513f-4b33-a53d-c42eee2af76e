#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔬 Graillon强制参数探测器
在插件界面已打开的情况下，强制探测所有可能的参数
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class GraillonForceDetect:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔬 Graillon强制参数探测器")
        self.root.geometry("1200x800")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 已知可能的Graillon参数名称
        self.possible_params = [
            # 音调相关
            "pitch", "pitch_shift", "pitch_correction", "transpose", "semitones",
            "coarse", "fine", "tune", "detune", "cents", "frequency",
            
            # Graillon特有参数
            "formant", "formant_shift", "voice_pitch", "vocal_range",
            "correction_strength", "correction_amount", "auto_tune",
            "pitch_snap", "pitch_bend", "vibrato", "tremolo",
            
            # 混合参数
            "wet", "dry", "mix", "blend", "output", "input", "gain",
            "wet_dry", "dry_wet", "wet_dry_mix", "output_gain",
            
            # 处理参数
            "smooth", "smoothing", "response", "speed", "attack", "release",
            "threshold", "sensitivity", "range", "scale", "amount",
            
            # 数字编号参数（很多VST使用数字）
            "param0", "param1", "param2", "param3", "param4", "param5",
            "param6", "param7", "param8", "param9", "param10", "param11",
            "param12", "param13", "param14", "param15", "param16", "param17",
            "param18", "param19", "param20",
            
            # 可能的内部参数名
            "p0", "p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "p9",
            "control0", "control1", "control2", "control3", "control4",
            "knob0", "knob1", "knob2", "knob3", "knob4", "knob5",
            "slider0", "slider1", "slider2", "slider3", "slider4",
        ]

        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 操作区域
        action_frame = ttk.LabelFrame(main_frame, text="🔬 强制探测", padding="10")
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        row1 = ttk.Frame(action_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(row1, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row1, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(row1, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        row2 = ttk.Frame(action_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        self.get_current_btn = ttk.Button(row2, text="📊 获取当前参数", 
                                         command=self.get_current_params, state="disabled")
        self.get_current_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.force_detect_btn = ttk.Button(row2, text="🔬 强制探测所有可能参数", 
                                          command=self.force_detect_params, state="disabled")
        self.force_detect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_found_btn = ttk.Button(row2, text="🧪 测试发现的参数", 
                                        command=self.test_found_params, state="disabled")
        self.test_found_btn.pack(side=tk.LEFT)
        
        row3 = ttk.Frame(action_frame)
        row3.pack(fill=tk.X)
        
        self.manipulate_ui_btn = ttk.Button(row3, text="🎛️ 界面操作检测", 
                                           command=self.detect_ui_manipulation, state="disabled")
        self.manipulate_ui_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.continuous_monitor_btn = ttk.Button(row3, text="📡 连续监控", 
                                               command=self.start_continuous_monitor, state="disabled")
        self.continuous_monitor_btn.pack(side=tk.LEFT)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="📋 探测结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 状态变量
        self.found_params = {}
        self.monitoring = False
        
        self.log("🔬 Graillon强制参数探测器")
        self.log("专门在插件界面已打开的情况下强制探测所有参数")
        self.log("=" * 60)
        self.log("💡 使用前提：")
        self.log("1. Graillon插件界面已打开（如你的截图所示）")
        self.log("2. VST滤镜已启用")
        self.log("3. 音频源正在播放")
        self.log("=" * 60)
        self.log(f"🎯 将尝试探测 {len(self.possible_params)} 个可能的参数名称")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        buttons = [self.get_current_btn, self.force_detect_btn, self.test_found_btn,
                  self.manipulate_ui_btn, self.continuous_monitor_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🚀 现在可以开始强制探测了！")
        self.log("💡 建议先点击'获取当前参数'查看基础状态")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        buttons = [self.get_current_btn, self.force_detect_btn, self.test_found_btn,
                  self.manipulate_ui_btn, self.continuous_monitor_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            return None

    def get_current_params(self):
        """获取当前参数"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("📊 获取当前VST参数...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.log(f"📋 当前发现 {len(settings)} 个参数:")
            self.log("=" * 50)

            for i, (key, value) in enumerate(settings.items(), 1):
                if key == 'plugin_path':
                    self.log(f"📁 {i:2d}. {key}: {value}")
                elif key in ['chunk_data', 'chunk_hash']:
                    self.log(f"💾 {i:2d}. {key}: [二进制数据 - {len(str(value))} 字符]")
                else:
                    try:
                        num_value = float(value)
                        self.log(f"🎚️ {i:2d}. {key}: {num_value}")
                        self.found_params[key] = num_value
                    except (ValueError, TypeError):
                        self.log(f"📝 {i:2d}. {key}: {value}")

            self.log("=" * 50)
            self.log(f"🎯 发现 {len(self.found_params)} 个数值参数可供测试")

            if len(settings) <= 3:
                self.log("⚠️ 参数很少！建议使用强制探测功能")
            else:
                self.log("✅ 参数数量正常，插件可能已完全加载")

        else:
            self.log("❌ 获取参数失败，请检查音频源和滤镜名称")

    def force_detect_params(self):
        """强制探测所有可能的参数"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("🔬 开始强制探测所有可能的参数...")
        self.log(f"🎯 将测试 {len(self.possible_params)} 个可能的参数名称")
        self.log("⏳ 这可能需要几分钟时间...")

        self.force_detect_btn.config(state="disabled", text="探测中...")

        def detect_thread():
            found_count = 0
            test_count = 0

            for param_name in self.possible_params:
                if not self.is_connected:
                    break

                test_count += 1
                self.root.after(0, self.log, f"🔍 [{test_count}/{len(self.possible_params)}] 测试参数: {param_name}")

                # 尝试设置一个测试值
                test_value = 0.5
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                }, timeout=3)

                if response and response.get('requestStatus', {}).get('result'):
                    # 验证是否真的设置成功
                    time.sleep(0.1)
                    verify_response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=3)

                    if verify_response and verify_response.get('requestStatus', {}).get('result'):
                        verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})

                        if param_name in verify_settings:
                            actual_value = verify_settings[param_name]
                            try:
                                if abs(float(actual_value) - test_value) < 0.1:
                                    found_count += 1
                                    self.found_params[param_name] = float(actual_value)
                                    self.root.after(0, self.log, f"✅ 发现有效参数: {param_name} = {actual_value}")

                                    # 分析参数可能的用途
                                    param_lower = param_name.lower()
                                    if any(word in param_lower for word in ['pitch', 'tune', 'transpose', 'semitone']):
                                        self.root.after(0, self.log, f"     🎵 -> 可能是音调参数")
                                    elif any(word in param_lower for word in ['formant', 'voice', 'vocal']):
                                        self.root.after(0, self.log, f"     🗣️ -> 可能是声音特征参数")
                                    elif any(word in param_lower for word in ['mix', 'wet', 'dry', 'blend']):
                                        self.root.after(0, self.log, f"     🔀 -> 可能是混合参数")

                            except (ValueError, TypeError):
                                pass

                # 小延迟避免过快请求
                time.sleep(0.05)

            self.root.after(0, self.on_detect_complete, found_count, test_count)

        threading.Thread(target=detect_thread, daemon=True).start()

    def on_detect_complete(self, found_count, test_count):
        """探测完成"""
        self.force_detect_btn.config(state="normal", text="🔬 强制探测所有可能参数")

        self.log("=" * 60)
        self.log(f"🎉 强制探测完成！")
        self.log(f"📊 测试了 {test_count} 个参数名称")
        self.log(f"✅ 发现 {found_count} 个有效参数")

        if found_count > 0:
            self.log("🎯 发现的有效参数:")
            for param_name, value in self.found_params.items():
                self.log(f"   - {param_name}: {value}")
            self.log("💡 现在可以点击'测试发现的参数'来验证这些参数的效果")
        else:
            self.log("😞 未发现新的有效参数")
            self.log("💡 建议:")
            self.log("   1. 确保Graillon插件界面已打开")
            self.log("   2. 确保音频源正在播放")
            self.log("   3. 尝试在插件界面中手动调整参数")
            self.log("   4. 使用'界面操作检测'功能")

    def test_found_params(self):
        """测试发现的参数"""
        if not self.found_params:
            self.log("❌ 没有发现的参数可供测试")
            return

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("🧪 测试发现的参数效果...")

        for param_name, current_value in self.found_params.items():
            self.log(f"🎚️ 测试参数: {param_name}")

            # 测试几个不同的值
            test_values = [0.0, 0.25, 0.5, 0.75, 1.0]

            for test_value in test_values:
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                })

                if response and response.get('requestStatus', {}).get('result'):
                    self.log(f"   ✅ {param_name} = {test_value}")
                    time.sleep(0.5)  # 给时间听效果
                else:
                    self.log(f"   ❌ 设置失败: {param_name} = {test_value}")

            # 恢复原值
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: current_value
                }
            })

            self.log(f"   🔄 已恢复原值: {param_name} = {current_value}")
            self.log("")

    def detect_ui_manipulation(self):
        """检测界面操作"""
        self.log("🎛️ 界面操作检测模式")
        self.log("💡 请在Graillon插件界面中调整参数，这里会实时显示变化")
        self.log("⏹️ 点击'连续监控'停止检测")

        self.start_continuous_monitor()

    def start_continuous_monitor(self):
        """开始连续监控"""
        if self.monitoring:
            self.stop_continuous_monitor()
            return

        self.monitoring = True
        self.continuous_monitor_btn.config(text="⏹️ 停止监控")

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 获取初始状态
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.last_settings = response.get('responseData', {}).get('filterSettings', {})
            self.log(f"📊 开始监控 {len(self.last_settings)} 个参数的变化...")
        else:
            self.log("❌ 无法获取初始状态")
            self.stop_continuous_monitor()
            return

        def monitor_loop():
            while self.monitoring and self.is_connected:
                try:
                    response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=2)

                    if response and response.get('requestStatus', {}).get('result'):
                        current_settings = response.get('responseData', {}).get('filterSettings', {})

                        # 检查变化
                        for key, value in current_settings.items():
                            if key in ['plugin_path', 'chunk_data', 'chunk_hash']:
                                continue

                            if key in self.last_settings:
                                old_value = self.last_settings[key]

                                try:
                                    old_num = float(old_value)
                                    new_num = float(value)

                                    if abs(new_num - old_num) > 0.001:
                                        diff = new_num - old_num
                                        self.root.after(0, self.log,
                                            f"🎚️ 参数变化: {key} = {old_num:.4f} → {new_num:.4f} ({diff:+.4f})")
                                        self.last_settings[key] = value

                                        # 记录到发现的参数中
                                        self.found_params[key] = new_num

                                except (ValueError, TypeError):
                                    if str(value) != str(old_value):
                                        self.root.after(0, self.log,
                                            f"📝 参数变化: {key} = {old_value} → {value}")
                                        self.last_settings[key] = value
                            else:
                                # 新参数出现
                                self.root.after(0, self.log, f"🆕 新参数: {key} = {value}")
                                self.last_settings[key] = value
                                try:
                                    self.found_params[key] = float(value)
                                except (ValueError, TypeError):
                                    pass

                    time.sleep(0.2)  # 每0.2秒检查一次，更敏感

                except Exception as e:
                    if self.monitoring:
                        self.root.after(0, self.log, f"❌ 监控错误: {e}")
                    time.sleep(1)

        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()

    def stop_continuous_monitor(self):
        """停止连续监控"""
        self.monitoring = False
        self.continuous_monitor_btn.config(text="📡 连续监控")
        self.log("⏹️ 停止连续监控")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GraillonForceDetect()
    app.run()
