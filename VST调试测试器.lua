obs = obslua

-- 简单的测试配置
local test_config = {
    source_name = "",
    filter_name = "",
    debug = true
}

-- 日志函数
local function log(message, level)
    obs.script_log(level or obs.LOG_INFO, "[VST调试] " .. tostring(message))
end

-- 获取所有音频源
local function get_audio_sources()
    local sources = {}
    local all_sources = obs.obs_enum_sources()
    if all_sources then
        for _, src in ipairs(all_sources) do
            local name = obs.obs_source_get_name(src)
            local source_type = obs.obs_source_get_id(src)
            table.insert(sources, name .. " (" .. source_type .. ")")
            obs.obs_source_release(src)
        end
    end
    return sources
end

-- 详细检查音频源和滤镜
local function detailed_check()
    log("=== 开始详细检查 ===")
    
    if not test_config.source_name or test_config.source_name == "" then
        log("❌ 未选择音频源", obs.LOG_WARNING)
        return
    end
    
    log("🔍 检查音频源: " .. test_config.source_name)
    
    -- 获取音频源
    local source = obs.obs_get_source_by_name(test_config.source_name)
    if not source then
        log("❌ 音频源不存在: " .. test_config.source_name, obs.LOG_ERROR)
        return
    end
    
    log("✅ 音频源存在")
    
    -- 检查音频源类型
    local source_id = obs.obs_source_get_id(source)
    log("📋 音频源类型: " .. source_id)
    
    if not test_config.filter_name or test_config.filter_name == "" then
        log("❌ 未输入滤镜名称", obs.LOG_WARNING)
        obs.obs_source_release(source)
        return
    end
    
    log("🔍 检查滤镜: " .. test_config.filter_name)
    
    -- 获取滤镜
    local filter = obs.obs_source_get_filter_by_name(source, test_config.filter_name)
    if not filter then
        log("❌ 滤镜不存在: " .. test_config.filter_name, obs.LOG_ERROR)
        log("💡 请检查滤镜名称是否与OBS中显示的完全一致", obs.LOG_INFO)
        obs.obs_source_release(source)
        return
    end
    
    log("✅ 滤镜存在")
    
    -- 检查滤镜类型
    local filter_id = obs.obs_source_get_id(filter)
    log("📋 滤镜类型: " .. filter_id)
    
    -- 获取滤镜设置
    local filter_settings = obs.obs_source_get_settings(filter)
    if not filter_settings then
        log("❌ 无法获取滤镜设置", obs.LOG_ERROR)
        obs.obs_source_release(filter)
        obs.obs_source_release(source)
        return
    end
    
    log("✅ 成功获取滤镜设置")
    
    -- 尝试读取一些常见参数
    log("🧪 尝试读取常见参数:")
    
    local common_params = {
        "pitch", "Pitch", "PITCH",
        "mix", "Mix", "MIX", 
        "wet", "Wet", "WET",
        "dry", "Dry", "DRY",
        "drive", "Drive", "DRIVE",
        "tone", "Tone", "TONE",
        "level", "Level", "LEVEL",
        "roomsize", "RoomSize", "ROOMSIZE",
        "damping", "Damping", "DAMPING",
        "formant", "Formant", "FORMANT"
    }
    
    for _, param in ipairs(common_params) do
        if obs.obs_data_has_user_value(filter_settings, param) then
            local value = obs.obs_data_get_double(filter_settings, param)
            log(string.format("  ✅ 找到参数: %s = %.3f", param, value))
        end
    end
    
    -- 清理资源
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    
    log("=== 检查完成 ===")
end

-- 测试设置单个参数
local function test_set_parameter()
    log("=== 开始参数设置测试 ===")
    
    if not test_config.source_name or test_config.source_name == "" then
        log("❌ 未选择音频源", obs.LOG_WARNING)
        return
    end
    
    if not test_config.filter_name or test_config.filter_name == "" then
        log("❌ 未输入滤镜名称", obs.LOG_WARNING)
        return
    end
    
    local source = obs.obs_get_source_by_name(test_config.source_name)
    if not source then
        log("❌ 音频源不存在", obs.LOG_ERROR)
        return
    end
    
    local filter = obs.obs_source_get_filter_by_name(source, test_config.filter_name)
    if not filter then
        log("❌ 滤镜不存在", obs.LOG_ERROR)
        obs.obs_source_release(source)
        return
    end
    
    local filter_settings = obs.obs_source_get_settings(filter)
    
    -- 测试常见参数
    local test_params = {
        {name = "pitch", value = 2.0},
        {name = "Pitch", value = 2.0},
        {name = "mix", value = 50.0},
        {name = "Mix", value = 50.0},
        {name = "drive", value = 60.0},
        {name = "Drive", value = 60.0}
    }
    
    for _, param in ipairs(test_params) do
        log(string.format("🧪 测试设置参数: %s = %.1f", param.name, param.value))
        
        -- 设置参数
        obs.obs_data_set_double(filter_settings, param.name, param.value)
        obs.obs_source_update(filter, filter_settings)
        
        -- 等待一下
        os.execute("sleep 1")
        
        -- 读取回来验证
        local read_value = obs.obs_data_get_double(filter_settings, param.name)
        if math.abs(read_value - param.value) < 0.01 then
            log(string.format("  ✅ 参数设置成功: %s = %.1f", param.name, read_value))
        else
            log(string.format("  ❌ 参数设置失败: %s (设置%.1f, 读取%.1f)", param.name, param.value, read_value))
        end
    end
    
    -- 清理资源
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    
    log("=== 参数设置测试完成 ===")
end

-- 脚本属性界面
function script_properties()
    local props = obs.obs_properties_create()
    
    -- 音频源选择
    local source_list = obs.obs_properties_add_list(props, "source_name", "🎤 选择音频源", 
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)
    
    for _, name in ipairs(get_audio_sources()) do
        local clean_name = name:match("^(.+) %(")
        if clean_name then
            obs.obs_property_list_add_string(source_list, clean_name, clean_name)
        else
            obs.obs_property_list_add_string(source_list, name, name)
        end
    end
    
    -- 滤镜名称输入
    obs.obs_properties_add_text(props, "filter_name", "🎛️ VST滤镜名称", obs.OBS_TEXT_DEFAULT)
    
    -- 测试按钮
    obs.obs_properties_add_button(props, "check_button", "🔍 详细检查", function()
        detailed_check()
        return true
    end)
    
    obs.obs_properties_add_button(props, "test_button", "🧪 测试参数设置", function()
        test_set_parameter()
        return true
    end)
    
    return props
end

-- 更新设置
function script_update(new_settings)
    test_config.source_name = obs.obs_data_get_string(new_settings, "source_name")
    test_config.filter_name = obs.obs_data_get_string(new_settings, "filter_name")
    
    log("配置更新: 源=" .. test_config.source_name .. ", 滤镜=" .. test_config.filter_name)
end

-- 脚本描述
function script_description()
    return [[🔧 VST调试测试器
==================
用于诊断VST插件控制问题的调试工具

🎯 功能：
1. 详细检查音频源和滤镜是否存在
2. 尝试读取VST插件的参数
3. 测试参数设置功能
4. 提供详细的调试日志

📋 使用步骤：
1. 选择包含VST滤镜的音频源
2. 输入VST滤镜的确切名称
3. 点击"详细检查"查看基本信息
4. 点击"测试参数设置"验证控制功能

💡 调试提示：
- 查看OBS脚本日志获取详细信息
- 确保滤镜名称与OBS中显示的完全一致
- 如果参数设置失败，可能是参数名称不正确]]
end
