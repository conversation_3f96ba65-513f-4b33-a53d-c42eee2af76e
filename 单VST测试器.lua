obs = obslua

-- 简单配置
local config = {
    source_name = "",
    filter_name = "",
    param_name = "pitch",
    test_value = 2.0,
    active = false,
    debug = true
}

local last_test = 0

-- 日志函数
local function log(message, level)
    if config.debug then
        obs.script_log(level or obs.LOG_INFO, "[单VST测试] " .. tostring(message))
    end
end

-- 获取音频源列表
local function get_audio_sources()
    local sources = {}
    local all_sources = obs.obs_enum_sources()
    if all_sources then
        for _, src in ipairs(all_sources) do
            local name = obs.obs_source_get_name(src)
            table.insert(sources, name)
            obs.obs_source_release(src)
        end
    end
    return sources
end

-- 测试VST参数控制
local function test_vst_control()
    log("=== 开始VST控制测试 ===")
    
    if not config.source_name or config.source_name == "" then
        log("❌ 未选择音频源", obs.LOG_WARNING)
        return false
    end
    
    if not config.filter_name or config.filter_name == "" then
        log("❌ 未输入滤镜名称", obs.LOG_WARNING)
        return false
    end
    
    -- 获取音频源
    local source = obs.obs_get_source_by_name(config.source_name)
    if not source then
        log("❌ 音频源不存在: " .. config.source_name, obs.LOG_ERROR)
        return false
    end
    
    log("✅ 找到音频源: " .. config.source_name)
    
    -- 获取滤镜
    local filter = obs.obs_source_get_filter_by_name(source, config.filter_name)
    if not filter then
        log("❌ 滤镜不存在: " .. config.filter_name, obs.LOG_ERROR)
        log("💡 请确保滤镜名称与OBS中显示的完全一致")
        obs.obs_source_release(source)
        return false
    end
    
    log("✅ 找到滤镜: " .. config.filter_name)
    
    -- 获取滤镜设置
    local filter_settings = obs.obs_source_get_settings(filter)
    if not filter_settings then
        log("❌ 无法获取滤镜设置", obs.LOG_ERROR)
        obs.obs_source_release(filter)
        obs.obs_source_release(source)
        return false
    end
    
    log("✅ 获取滤镜设置成功")
    
    -- 生成测试值（在原值基础上变化）
    local test_value = config.test_value + (math.random() - 0.5) * 2
    
    log(string.format("🧪 尝试设置参数: %s = %.2f", config.param_name, test_value))
    
    -- 尝试多种方式设置参数
    local param_variants = {
        config.param_name,
        string.lower(config.param_name),
        string.upper(config.param_name),
        config.param_name:gsub("^%l", string.upper) -- 首字母大写
    }
    
    local success = false
    for _, param in ipairs(param_variants) do
        -- 尝试设置为double
        obs.obs_data_set_double(filter_settings, param, test_value)
        
        -- 尝试设置为int
        obs.obs_data_set_int(filter_settings, param, math.floor(test_value))
        
        -- 尝试设置为string
        obs.obs_data_set_string(filter_settings, param, tostring(test_value))
        
        log(string.format("  尝试参数名: %s", param))
    end
    
    -- 更新滤镜
    obs.obs_source_update(filter, filter_settings)
    log("✅ 滤镜设置已更新")
    
    -- 验证设置是否生效（读取回来）
    local read_value = obs.obs_data_get_double(filter_settings, config.param_name)
    if math.abs(read_value - test_value) < 0.01 then
        log(string.format("✅ 参数设置验证成功: %.2f", read_value))
        success = true
    else
        log(string.format("⚠️ 参数设置验证失败: 设置%.2f, 读取%.2f", test_value, read_value))
        log("💡 这可能是正常的，某些VST插件不支持参数回读")
        success = true -- 仍然认为成功，因为我们已经发送了设置
    end
    
    -- 清理资源
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    
    log("=== VST控制测试完成 ===")
    return success
end

-- 连续测试函数
local function continuous_test()
    if config.active then
        if os.clock() - last_test >= 3.0 then -- 每3秒测试一次
            test_vst_control()
            last_test = os.clock()
        end
    end
end

-- 脚本属性
function script_properties()
    local props = obs.obs_properties_create()
    
    -- 音频源选择
    local source_list = obs.obs_properties_add_list(props, "source_name", "🎤 音频源", 
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)
    
    for _, name in ipairs(get_audio_sources()) do
        obs.obs_property_list_add_string(source_list, name, name)
    end
    
    -- 滤镜名称
    obs.obs_properties_add_text(props, "filter_name", "🎛️ VST滤镜名称", obs.OBS_TEXT_DEFAULT)
    
    -- 参数名称
    obs.obs_properties_add_text(props, "param_name", "🔧 参数名称", obs.OBS_TEXT_DEFAULT)
    
    -- 测试值
    obs.obs_properties_add_float(props, "test_value", "🎯 测试值", -100.0, 100.0, 0.1)
    
    -- 控制开关
    obs.obs_properties_add_bool(props, "active", "🔄 启用连续测试")
    obs.obs_properties_add_bool(props, "debug", "🐛 调试日志")
    
    -- 手动测试按钮
    obs.obs_properties_add_button(props, "test_button", "🧪 立即测试", function()
        test_vst_control()
        return true
    end)
    
    return props
end

-- 更新设置
function script_update(new_settings)
    config.source_name = obs.obs_data_get_string(new_settings, "source_name")
    config.filter_name = obs.obs_data_get_string(new_settings, "filter_name")
    config.param_name = obs.obs_data_get_string(new_settings, "param_name")
    config.test_value = obs.obs_data_get_double(new_settings, "test_value")
    config.active = obs.obs_data_get_bool(new_settings, "active")
    config.debug = obs.obs_data_get_bool(new_settings, "debug")
    
    log("设置已更新")
end

-- 主循环
function script_tick(seconds)
    continuous_test()
end

-- 脚本加载
function script_load(settings_data)
    last_test = 0
    log("单VST测试器已加载")
end

-- 脚本描述
function script_description()
    return [[🧪 单VST插件测试器
====================
专门用于测试单个VST插件参数控制的简化工具

🎯 功能：
- 测试单个VST插件的参数控制
- 支持手动测试和连续测试
- 详细的调试日志输出
- 多种参数名称格式尝试

📋 使用步骤：
1. 选择包含VST滤镜的音频源
2. 输入VST滤镜的确切名称
3. 输入要测试的参数名称（如pitch、mix等）
4. 设置测试值
5. 点击"立即测试"或启用"连续测试"

💡 常见参数名：
- Graillon: pitch, formant, mix
- TAL-Reverb: roomsize, damping, mix
- TSE808: drive, tone, level

⚠️ 注意：
- 滤镜名称必须完全匹配
- 参数名称区分大小写
- 查看日志获取详细信息]]
end
