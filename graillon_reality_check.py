#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 Graillon真实性检查器
验证获取到的参数是否真的是Graillon的实际参数
通过界面操作反向验证参数的真实性
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import websocket
import threading
import time
from datetime import datetime

class GraillonRealityCheck:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 Graillon真实性检查器")
        self.root.geometry("1000x700")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 监控状态
        self.monitoring = False
        self.baseline_params = {}
        self.detected_changes = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="⚙️ 配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X)
        
        ttk.Label(row1, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(row1, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row1, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(row1, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # 真实性检查区域
        check_frame = ttk.LabelFrame(main_frame, text="🔍 真实性检查", padding="10")
        check_frame.pack(fill=tk.X, pady=(0, 10))
        
        row2 = ttk.Frame(check_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        self.get_baseline_btn = ttk.Button(row2, text="📊 建立基线", 
                                          command=self.establish_baseline, state="disabled")
        self.get_baseline_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.start_monitor_btn = ttk.Button(row2, text="👁️ 开始界面监控", 
                                           command=self.start_ui_monitoring, state="disabled")
        self.start_monitor_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_monitor_btn = ttk.Button(row2, text="⏹️ 停止监控", 
                                          command=self.stop_monitoring, state="disabled")
        self.stop_monitor_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.verify_btn = ttk.Button(row2, text="✅ 验证发现的参数", 
                                    command=self.verify_discovered_params, state="disabled")
        self.verify_btn.pack(side=tk.LEFT)
        
        row3 = ttk.Frame(check_frame)
        row3.pack(fill=tk.X)
        
        self.test_fake_btn = ttk.Button(row3, text="🧪 测试虚假参数", 
                                       command=self.test_fake_params, state="disabled")
        self.test_fake_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.compare_btn = ttk.Button(row3, text="🔄 对比测试", 
                                     command=self.compare_test, state="disabled")
        self.compare_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reality_score_btn = ttk.Button(row3, text="📈 真实性评分", 
                                           command=self.calculate_reality_score, state="disabled")
        self.reality_score_btn.pack(side=tk.LEFT)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="📋 检查结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🔍 Graillon真实性检查器")
        self.log("专门验证获取到的参数是否真的是Graillon的实际控制参数")
        self.log("=" * 60)
        self.log("🎯 检查原理:")
        self.log("1. 建立参数基线 - 记录当前所有参数值")
        self.log("2. 界面监控 - 监控你在Graillon界面中的操作")
        self.log("3. 参数变化检测 - 看哪些参数真的随界面操作而变化")
        self.log("4. 虚假参数测试 - 测试设置不存在的参数")
        self.log("5. 真实性评分 - 综合评估参数的真实性")
        self.log("=" * 60)
        self.log("💡 使用方法:")
        self.log("1. 确保Graillon插件界面已打开")
        self.log("2. 点击'连接OBS'")
        self.log("3. 点击'建立基线'")
        self.log("4. 点击'开始界面监控'")
        self.log("5. 在Graillon界面中调整各种旋钮和滑块")
        self.log("6. 观察哪些参数真的发生了变化")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        buttons = [self.get_baseline_btn, self.start_monitor_btn, self.verify_btn,
                  self.test_fake_btn, self.compare_btn, self.reality_score_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🚀 现在可以开始真实性检查了！")
        self.log("💡 建议先点击'建立基线'")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.monitoring:
            self.stop_monitoring()
            
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        buttons = [self.get_baseline_btn, self.start_monitor_btn, self.stop_monitor_btn,
                  self.verify_btn, self.test_fake_btn, self.compare_btn, self.reality_score_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            return None

    def establish_baseline(self):
        """建立参数基线"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("📊 建立参数基线...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.baseline_params = {}
            numeric_count = 0
            other_count = 0

            self.log("📋 基线参数:")
            self.log("-" * 50)

            for key, value in settings.items():
                if key == 'plugin_path':
                    self.log(f"📁 {key}: {value}")
                    continue
                elif key in ['chunk_data', 'chunk_hash']:
                    self.log(f"💾 {key}: [二进制数据 - {len(str(value))} 字符]")
                    other_count += 1
                    self.baseline_params[key] = value
                    continue

                try:
                    num_value = float(value)
                    self.baseline_params[key] = num_value
                    numeric_count += 1
                    self.log(f"🎚️ {key}: {num_value}")
                except (ValueError, TypeError):
                    self.baseline_params[key] = value
                    other_count += 1
                    self.log(f"📝 {key}: {value}")

            self.log("-" * 50)
            self.log(f"✅ 基线建立完成！")
            self.log(f"📊 数值参数: {numeric_count} 个")
            self.log(f"📝 其他参数: {other_count} 个")
            self.log(f"📋 总参数: {len(self.baseline_params)} 个")

            if numeric_count <= 3:
                self.log("⚠️ 数值参数很少，可能参数未完全暴露")
                self.log("💡 建议:")
                self.log("   1. 确保Graillon插件界面已打开")
                self.log("   2. 在插件界面中调整一些参数")
                self.log("   3. 重新建立基线")
            else:
                self.log("✅ 参数数量正常，可以开始监控")

        else:
            self.log("❌ 建立基线失败")

    def start_ui_monitoring(self):
        """开始界面监控"""
        if self.monitoring:
            self.log("⚠️ 监控已在运行中")
            return

        if not self.baseline_params:
            self.log("❌ 请先建立基线！")
            messagebox.showwarning("错误", "请先点击'建立基线'按钮")
            return

        self.monitoring = True
        self.start_monitor_btn.config(state="disabled")
        self.stop_monitor_btn.config(state="normal")
        self.detected_changes = {}

        self.log("👁️ 开始界面监控...")
        self.log("🎛️ 请在Graillon插件界面中调整各种参数")
        self.log("📊 这里会实时显示哪些参数真的发生了变化")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        def monitor_loop():
            change_count = 0

            while self.monitoring and self.is_connected:
                try:
                    response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=2)

                    if response and response.get('requestStatus', {}).get('result'):
                        current_settings = response.get('responseData', {}).get('filterSettings', {})

                        # 检查每个参数的变化
                        for key, current_value in current_settings.items():
                            if key not in self.baseline_params:
                                # 新参数出现
                                self.root.after(0, self.log, f"🆕 新参数出现: {key} = {current_value}")
                                self.baseline_params[key] = current_value
                                continue

                            baseline_value = self.baseline_params[key]

                            # 检查数值参数变化
                            try:
                                baseline_num = float(baseline_value)
                                current_num = float(current_value)

                                # 使用极敏感的检测阈值
                                if abs(current_num - baseline_num) > 0.0001:
                                    diff = current_num - baseline_num
                                    change_count += 1

                                    # 记录变化
                                    if key not in self.detected_changes:
                                        self.detected_changes[key] = {
                                            'baseline': baseline_num,
                                            'changes': [],
                                            'change_count': 0
                                        }

                                    self.detected_changes[key]['changes'].append({
                                        'from': baseline_num,
                                        'to': current_num,
                                        'diff': diff,
                                        'timestamp': time.time()
                                    })
                                    self.detected_changes[key]['change_count'] += 1

                                    self.root.after(0, self.log,
                                        f"🎚️ 真实变化 #{change_count}: {key}")
                                    self.root.after(0, self.log,
                                        f"   {baseline_num:.6f} → {current_num:.6f} ({diff:+.6f})")

                                    # 分析参数类型
                                    param_type = self.analyze_param_type(key, diff)
                                    if param_type:
                                        self.root.after(0, self.log, f"   🎯 {param_type}")

                                    # 更新基线
                                    self.baseline_params[key] = current_num

                            except (ValueError, TypeError):
                                # 检查非数值参数变化
                                if str(current_value) != str(baseline_value):
                                    change_count += 1

                                    if key not in self.detected_changes:
                                        self.detected_changes[key] = {
                                            'baseline': baseline_value,
                                            'changes': [],
                                            'change_count': 0
                                        }

                                    self.detected_changes[key]['changes'].append({
                                        'from': baseline_value,
                                        'to': current_value,
                                        'timestamp': time.time()
                                    })
                                    self.detected_changes[key]['change_count'] += 1

                                    self.root.after(0, self.log,
                                        f"📝 真实变化 #{change_count}: {key}")
                                    self.root.after(0, self.log,
                                        f"   {baseline_value} → {current_value}")

                                    # 更新基线
                                    self.baseline_params[key] = current_value

                    time.sleep(0.1)  # 极高频率检测，0.1秒一次

                except Exception as e:
                    if self.monitoring:
                        self.root.after(0, self.log, f"❌ 监控错误: {e}")
                    time.sleep(1)

        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return

        self.monitoring = False
        self.start_monitor_btn.config(state="normal")
        self.stop_monitor_btn.config(state="disabled")

        self.log("⏹️ 停止界面监控")
        self.log("=" * 50)
        self.log("📊 监控总结:")

        if self.detected_changes:
            self.log(f"✅ 发现 {len(self.detected_changes)} 个真实变化的参数:")

            for param_name, change_info in self.detected_changes.items():
                change_count = change_info['change_count']
                self.log(f"   🎚️ {param_name}: {change_count} 次变化")

                # 显示最近的变化
                if change_info['changes']:
                    latest_change = change_info['changes'][-1]
                    if 'diff' in latest_change:
                        self.log(f"      最新: {latest_change['from']:.3f} → {latest_change['to']:.3f}")
                    else:
                        self.log(f"      最新: {latest_change['from']} → {latest_change['to']}")

            self.log("🎉 这些参数很可能是Graillon的真实控制参数！")
        else:
            self.log("😞 未检测到任何参数变化")
            self.log("💡 可能的原因:")
            self.log("   1. 插件界面未打开或未响应")
            self.log("   2. 参数变化太小，未达到检测阈值")
            self.log("   3. VST插件使用了特殊的参数同步机制")
            self.log("   4. 需要在插件界面中进行更明显的调整")

    def analyze_param_type(self, param_name, diff):
        """分析参数类型"""
        param_lower = param_name.lower()

        # 音调相关
        if any(word in param_lower for word in ['pitch', 'tune', 'transpose', 'semitone', 'coarse', 'fine']):
            if abs(diff) > 0.1:
                return "可能是音调参数 🎵"

        # 共振峰相关
        if any(word in param_lower for word in ['formant', 'voice', 'vocal']):
            return "可能是声音特征参数 🗣️"

        # 混合相关
        if any(word in param_lower for word in ['mix', 'wet', 'dry', 'blend']):
            return "可能是混合参数 🔀"

        # 校正相关
        if any(word in param_lower for word in ['correct', 'auto', 'snap']):
            return "可能是校正参数 🎯"

        return None

    def verify_discovered_params(self):
        """验证发现的参数"""
        if not self.detected_changes:
            self.log("❌ 没有发现的参数可供验证")
            self.log("💡 请先进行界面监控，发现真实变化的参数")
            return

        self.log("✅ 开始验证发现的真实参数...")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        verified_params = {}

        for param_name, change_info in self.detected_changes.items():
            self.log(f"🧪 验证参数: {param_name}")

            # 获取当前值
            response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if not (response and response.get('requestStatus', {}).get('result')):
                self.log(f"   ❌ 无法获取当前值")
                continue

            current_settings = response.get('responseData', {}).get('filterSettings', {})
            if param_name not in current_settings:
                self.log(f"   ❌ 参数不存在")
                continue

            original_value = current_settings[param_name]
            self.log(f"   📊 当前值: {original_value}")

            # 尝试设置不同的测试值
            try:
                original_num = float(original_value)
                test_values = [
                    original_num - 1.0,
                    original_num + 1.0,
                    original_num * 1.5 if original_num != 0 else 1.5
                ]
            except (ValueError, TypeError):
                test_values = ["test_value_1", "test_value_2"]

            success_count = 0
            for test_value in test_values[:2]:  # 只测试前两个值
                self.log(f"   🎯 测试值: {test_value}")

                # 设置测试值
                set_response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                })

                if set_response and set_response.get('requestStatus', {}).get('result'):
                    # 验证设置
                    time.sleep(0.3)
                    verify_response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    })

                    if verify_response and verify_response.get('requestStatus', {}).get('result'):
                        verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                        if param_name in verify_settings:
                            actual_value = verify_settings[param_name]

                            try:
                                if abs(float(actual_value) - float(test_value)) < 0.01:
                                    self.log(f"      ✅ 设置成功: {actual_value}")
                                    success_count += 1
                                else:
                                    self.log(f"      ⚠️ 值不匹配: {actual_value}")
                            except (ValueError, TypeError):
                                if str(actual_value) == str(test_value):
                                    self.log(f"      ✅ 设置成功: {actual_value}")
                                    success_count += 1
                                else:
                                    self.log(f"      ⚠️ 值不匹配: {actual_value}")
                        else:
                            self.log(f"      ❌ 验证失败")
                    else:
                        self.log(f"      ❌ 验证请求失败")
                else:
                    self.log(f"      ❌ 设置失败")

            # 恢复原始值
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: original_value
                }
            })

            # 评估验证结果
            if success_count >= 1:
                verified_params[param_name] = {
                    'success_rate': success_count / 2,
                    'change_count': change_info['change_count']
                }
                self.log(f"   🎉 验证通过！成功率: {success_count}/2")
            else:
                self.log(f"   ❌ 验证失败")

            self.log("")

        self.log("=" * 50)
        self.log("📊 验证总结:")
        if verified_params:
            self.log(f"✅ {len(verified_params)} 个参数通过验证:")
            for param_name, info in verified_params.items():
                self.log(f"   🎚️ {param_name}: 成功率 {info['success_rate']:.0%}, "
                        f"变化次数 {info['change_count']}")
            self.log("🎉 这些参数确认是真实有效的Graillon控制参数！")
        else:
            self.log("😞 没有参数通过验证")

    def test_fake_params(self):
        """测试虚假参数"""
        self.log("🧪 测试虚假参数...")
        self.log("这将尝试设置一些不存在的参数，验证VST的响应")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 虚假参数列表
        fake_params = [
            "fake_pitch_control",
            "nonexistent_param",
            "dummy_formant",
            "test_parameter_123",
            "invalid_control",
            "fake_wet_dry_mix"
        ]

        fake_success_count = 0

        for fake_param in fake_params:
            self.log(f"🎭 测试虚假参数: {fake_param}")

            # 尝试设置虚假参数
            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    fake_param: 0.5
                }
            })

            if response and response.get('requestStatus', {}).get('result'):
                self.log(f"   ⚠️ 虚假参数设置'成功' - 这很可疑！")
                fake_success_count += 1

                # 验证是否真的设置了
                time.sleep(0.3)
                verify_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                })

                if verify_response and verify_response.get('requestStatus', {}).get('result'):
                    verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                    if fake_param in verify_settings:
                        self.log(f"   🚨 虚假参数真的被添加了！值: {verify_settings[fake_param]}")
                        self.log(f"   🔍 这说明VST接受任意参数名，参数可能不是真实的")
                    else:
                        self.log(f"   ✅ 虚假参数未被添加，这是正常的")
            else:
                self.log(f"   ✅ 虚假参数设置失败，这是正常的")

        self.log("=" * 50)
        self.log("🧪 虚假参数测试结果:")
        self.log(f"📊 {fake_success_count}/{len(fake_params)} 个虚假参数'设置成功'")

        if fake_success_count > 0:
            self.log("🚨 警告：VST接受了虚假参数！")
            self.log("💡 这意味着:")
            self.log("   1. VST可能接受任意参数名")
            self.log("   2. 之前发现的参数可能不是真实的控制参数")
            self.log("   3. 需要通过界面操作来验证参数的真实性")
        else:
            self.log("✅ VST正确拒绝了虚假参数")
            self.log("💡 这是好消息，说明参数验证机制正常")

    def compare_test(self):
        """对比测试"""
        self.log("🔄 开始对比测试...")
        self.log("这将对比界面操作和API设置的效果")
        self.log("=" * 50)

        if not self.detected_changes:
            self.log("❌ 没有发现的真实参数可供对比")
            self.log("💡 请先进行界面监控")
            return

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 选择一个最活跃的参数进行对比测试
        most_active_param = max(self.detected_changes.items(),
                               key=lambda x: x[1]['change_count'])
        param_name = most_active_param[0]
        param_info = most_active_param[1]

        self.log(f"🎯 选择最活跃的参数进行对比: {param_name}")
        self.log(f"   变化次数: {param_info['change_count']}")

        # 获取当前值
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if not (response and response.get('requestStatus', {}).get('result')):
            self.log("❌ 无法获取当前参数值")
            return

        current_settings = response.get('responseData', {}).get('filterSettings', {})
        if param_name not in current_settings:
            self.log("❌ 参数不存在")
            return

        original_value = current_settings[param_name]
        self.log(f"📊 当前值: {original_value}")

        # 对比测试序列
        try:
            original_num = float(original_value)
            test_sequence = [
                original_num + 2.0,
                original_num - 2.0,
                original_num + 5.0,
                original_num
            ]
        except (ValueError, TypeError):
            self.log("⚠️ 参数不是数值类型，跳过对比测试")
            return

        self.log("🔄 开始对比测试序列...")
        self.log("💡 请观察音频效果，并在Graillon界面中观察参数变化")

        for i, test_value in enumerate(test_sequence, 1):
            self.log(f"\n🧪 对比测试 {i}/4: 设置 {param_name} = {test_value}")

            # 通过API设置参数
            set_response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: test_value
                }
            })

            if set_response and set_response.get('requestStatus', {}).get('result'):
                self.log("   ✅ API设置成功")

                # 验证设置
                time.sleep(0.5)
                verify_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                })

                if verify_response and verify_response.get('requestStatus', {}).get('result'):
                    verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                    if param_name in verify_settings:
                        actual_value = verify_settings[param_name]
                        self.log(f"   📊 实际值: {actual_value}")

                        if abs(float(actual_value) - test_value) < 0.01:
                            self.log("   🎯 API设置已确认")
                            self.log("   🎧 请检查音频效果是否变化")
                            self.log("   👁️ 请检查Graillon界面中对应控件是否移动")
                        else:
                            self.log(f"   ⚠️ 值不匹配")
                    else:
                        self.log("   ❌ 验证失败")
                else:
                    self.log("   ❌ 验证请求失败")
            else:
                self.log("   ❌ API设置失败")

            # 等待用户观察
            self.log("   ⏳ 等待3秒供观察...")
            time.sleep(3)

        self.log("\n🔄 对比测试完成！")
        self.log("💡 如果:")
        self.log("   ✅ 音频效果有变化 + Graillon界面控件有移动 = 参数真实有效")
        self.log("   ❌ 音频无变化 或 界面控件无移动 = 参数可能是虚假的")

    def calculate_reality_score(self):
        """计算真实性评分"""
        self.log("📈 计算参数真实性评分...")
        self.log("=" * 50)

        if not self.detected_changes:
            self.log("❌ 没有数据可供评分")
            self.log("💡 请先进行界面监控和参数验证")
            return

        total_score = 0
        max_score = 0
        param_scores = {}

        for param_name, change_info in self.detected_changes.items():
            score = 0
            max_param_score = 100

            # 1. 变化频率评分 (40分)
            change_count = change_info['change_count']
            if change_count >= 5:
                change_score = 40
            elif change_count >= 3:
                change_score = 30
            elif change_count >= 1:
                change_score = 20
            else:
                change_score = 0
            score += change_score

            # 2. 参数名称评分 (30分)
            param_lower = param_name.lower()
            name_score = 0

            # 音调相关关键词
            if any(word in param_lower for word in ['pitch', 'tune', 'transpose', 'semitone']):
                name_score += 15

            # Graillon特有关键词
            if any(word in param_lower for word in ['formant', 'correct', 'voice']):
                name_score += 10

            # 通用音频关键词
            if any(word in param_lower for word in ['mix', 'wet', 'dry', 'gain']):
                name_score += 5

            # 避免通用VST包装器参数
            if any(word in param_lower for word in ['chunk', 'plugin', 'path']):
                name_score -= 10

            name_score = max(0, min(30, name_score))
            score += name_score

            # 3. 数值范围评分 (30分)
            range_score = 0
            if change_info['changes']:
                values = []
                for change in change_info['changes']:
                    if 'diff' in change:
                        values.extend([change['from'], change['to']])

                if values:
                    value_range = max(values) - min(values)

                    # 音调参数通常在-24到24之间
                    if 0.1 <= value_range <= 48:
                        range_score = 30
                    elif 0.01 <= value_range <= 100:
                        range_score = 20
                    elif value_range > 0:
                        range_score = 10

            score += range_score

            # 计算百分比得分
            percentage = (score / max_param_score) * 100
            param_scores[param_name] = {
                'score': score,
                'percentage': percentage,
                'change_score': change_score,
                'name_score': name_score,
                'range_score': range_score
            }

            total_score += score
            max_score += max_param_score

        # 显示评分结果
        self.log("📊 参数真实性评分结果:")
        self.log("-" * 60)

        # 按得分排序
        sorted_params = sorted(param_scores.items(), key=lambda x: x[1]['percentage'], reverse=True)

        for param_name, scores in sorted_params:
            percentage = scores['percentage']

            if percentage >= 80:
                status = "🟢 极可能真实"
            elif percentage >= 60:
                status = "🟡 可能真实"
            elif percentage >= 40:
                status = "🟠 可疑"
            else:
                status = "🔴 可能虚假"

            self.log(f"{status} {param_name}: {percentage:.1f}%")
            self.log(f"   变化频率: {scores['change_score']}/40")
            self.log(f"   参数名称: {scores['name_score']}/30")
            self.log(f"   数值范围: {scores['range_score']}/30")
            self.log("")

        # 总体评分
        overall_percentage = (total_score / max_score) * 100 if max_score > 0 else 0

        self.log("-" * 60)
        self.log(f"📈 总体真实性评分: {overall_percentage:.1f}%")

        if overall_percentage >= 70:
            self.log("🎉 参数真实性很高！大部分参数可能是真实的Graillon控制参数")
        elif overall_percentage >= 50:
            self.log("🤔 参数真实性中等，需要进一步验证")
        else:
            self.log("😞 参数真实性较低，可能大部分是虚假或无效参数")

        self.log("=" * 50)

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GraillonRealityCheck()
    app.run()
