#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 OBS VST插件真实数据探测器
直接从OBS API获取VST插件的完整真实信息
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class OBSVSTInspector:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 OBS VST插件真实数据探测器")
        self.root.geometry("1400x900")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0

        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 探测区域
        inspect_frame = ttk.LabelFrame(main_frame, text="🔍 数据探测", padding="10")
        inspect_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：基础探测
        row1 = ttk.Frame(inspect_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        self.get_scenes_btn = ttk.Button(row1, text="📋 获取所有场景", 
                                        command=self.get_all_scenes, state="disabled")
        self.get_scenes_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.get_sources_btn = ttk.Button(row1, text="🎯 获取所有音频源", 
                                         command=self.get_all_sources, state="disabled")
        self.get_sources_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.get_filters_btn = ttk.Button(row1, text="🎛️ 获取所有滤镜", 
                                         command=self.get_all_filters, state="disabled")
        self.get_filters_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 第二行：VST专项探测
        row2 = ttk.Frame(inspect_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row2, text="目标音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        self.source_entry = ttk.Entry(row2, textvariable=self.source_var, width=15)
        self.source_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        self.inspect_source_btn = ttk.Button(row2, text="🔬 深度探测此音频源", 
                                           command=self.deep_inspect_source, state="disabled")
        self.inspect_source_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.monitor_changes_btn = ttk.Button(row2, text="📡 监控参数变化", 
                                            command=self.monitor_parameter_changes, state="disabled")
        self.monitor_changes_btn.pack(side=tk.LEFT)
        
        # 第三行：API测试
        row3 = ttk.Frame(inspect_frame)
        row3.pack(fill=tk.X)
        
        self.test_all_apis_btn = ttk.Button(row3, text="🧪 测试所有相关API", 
                                          command=self.test_all_apis, state="disabled")
        self.test_all_apis_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.export_data_btn = ttk.Button(row3, text="💾 导出数据", 
                                        command=self.export_data, state="disabled")
        self.export_data_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 探测结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🔍 OBS VST插件真实数据探测器启动")
        self.log("这个工具将直接从OBS API获取完整的真实VST数据")
        self.log("=" * 60)
        self.log("📋 使用步骤:")
        self.log("1. 点击'连接OBS'")
        self.log("2. 点击'获取所有音频源'查看可用的音频源")
        self.log("3. 填写目标音频源名称")
        self.log("4. 点击'深度探测此音频源'获取完整VST数据")
        self.log("=" * 60)
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有按钮
        buttons = [self.get_scenes_btn, self.get_sources_btn, self.get_filters_btn,
                  self.inspect_source_btn, self.monitor_changes_btn, self.test_all_apis_btn,
                  self.export_data_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🎉 所有探测功能已启用！")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.get_scenes_btn, self.get_sources_btn, self.get_filters_btn,
                  self.inspect_source_btn, self.monitor_changes_btn, self.test_all_apis_btn,
                  self.export_data_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.log(f"📤 发送请求: {request_type}")
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        self.log(f"📥 收到响应: {request_type}")
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except Exception as e:
                    self.log(f"❌ 接收响应时出错: {e}")
                    continue
                    
            self.log(f"⏰ 请求超时: {request_type}")
            return None
            
        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None

    def get_all_scenes(self):
        """获取所有场景"""
        self.log("📋 获取所有场景...")
        response = self.send_request("GetSceneList")
        
        if response and response.get('requestStatus', {}).get('result'):
            scenes = response.get('responseData', {}).get('scenes', [])
            current_scene = response.get('responseData', {}).get('currentProgramSceneName', '')
            
            self.log(f"✅ 找到 {len(scenes)} 个场景")
            self.log(f"🎬 当前场景: {current_scene}")
            self.log("-" * 50)
            
            for scene in scenes:
                scene_name = scene.get('sceneName', '')
                scene_index = scene.get('sceneIndex', 0)
                self.log(f"  📋 场景 {scene_index}: {scene_name}")
                
        else:
            self.log("❌ 获取场景列表失败")

    def get_all_sources(self):
        """获取所有音频源"""
        self.log("🎯 获取所有音频源...")
        response = self.send_request("GetInputList")

        if response and response.get('requestStatus', {}).get('result'):
            inputs = response.get('responseData', {}).get('inputs', [])

            self.log(f"✅ 找到 {len(inputs)} 个输入源")
            self.log("-" * 50)

            audio_sources = []
            for inp in inputs:
                input_name = inp.get('inputName', '')
                input_kind = inp.get('inputKind', '')

                # 检查是否是音频相关的源
                if any(keyword in input_kind.lower() for keyword in ['audio', 'media', 'capture', 'source']):
                    audio_sources.append(input_name)
                    self.log(f"  🎵 {input_name} ({input_kind})")
                else:
                    self.log(f"  📺 {input_name} ({input_kind})")

            self.log("-" * 50)
            self.log(f"🎵 音频相关源: {len(audio_sources)} 个")

            # 自动填入第一个音频源
            if audio_sources:
                self.source_var.set(audio_sources[0])
                self.log(f"💡 已自动选择: {audio_sources[0]}")

        else:
            self.log("❌ 获取输入源列表失败")

    def get_all_filters(self):
        """获取所有滤镜"""
        self.log("🎛️ 获取所有滤镜...")

        # 先获取所有输入源
        response = self.send_request("GetInputList")
        if not (response and response.get('requestStatus', {}).get('result')):
            self.log("❌ 无法获取输入源列表")
            return

        inputs = response.get('responseData', {}).get('inputs', [])
        self.log(f"📋 检查 {len(inputs)} 个输入源的滤镜...")
        self.log("=" * 60)

        total_filters = 0
        vst_filters = []

        for inp in inputs:
            input_name = inp.get('inputName', '')
            self.log(f"\n🎯 检查源: {input_name}")

            # 获取此源的所有滤镜
            filter_response = self.send_request("GetSourceFilterList", {
                "sourceName": input_name
            })

            if filter_response and filter_response.get('requestStatus', {}).get('result'):
                filters = filter_response.get('responseData', {}).get('filters', [])

                if filters:
                    self.log(f"  📊 找到 {len(filters)} 个滤镜:")
                    for f in filters:
                        filter_name = f.get('filterName', '')
                        filter_kind = f.get('filterKind', '')
                        filter_enabled = f.get('filterEnabled', False)

                        status = "✅" if filter_enabled else "❌"
                        self.log(f"    {status} {filter_name} ({filter_kind})")

                        # 检查是否是VST滤镜
                        if 'vst' in filter_kind.lower():
                            vst_filters.append((input_name, filter_name, filter_kind))
                            self.log(f"      🎛️ VST滤镜发现！")

                        total_filters += 1
                else:
                    self.log("  📭 无滤镜")
            else:
                self.log("  ❌ 获取滤镜失败")

        self.log("\n" + "=" * 60)
        self.log(f"📊 总计: {total_filters} 个滤镜")
        self.log(f"🎛️ VST滤镜: {len(vst_filters)} 个")

        if vst_filters:
            self.log("\n🎛️ 发现的VST滤镜:")
            for source_name, filter_name, filter_kind in vst_filters:
                self.log(f"  📍 {source_name} -> {filter_name} ({filter_kind})")

    def deep_inspect_source(self):
        """深度探测指定音频源"""
        source_name = self.source_var.get().strip()
        if not source_name:
            self.log("❌ 请填写音频源名称")
            return

        self.log(f"🔬 开始深度探测音频源: {source_name}")
        self.log("=" * 60)

        # 1. 检查源是否存在
        self.log("📋 步骤1: 验证音频源存在性...")
        source_response = self.send_request("GetInputList")

        if source_response and source_response.get('requestStatus', {}).get('result'):
            inputs = source_response.get('responseData', {}).get('inputs', [])
            source_names = [inp.get('inputName', '') for inp in inputs]

            if source_name not in source_names:
                self.log(f"❌ 未找到音频源: {source_name}")
                self.log("📋 可用的音频源:")
                for name in source_names[:10]:
                    self.log(f"   - {name}")
                return
            else:
                self.log(f"✅ 确认音频源存在: {source_name}")

        # 2. 获取源的详细信息
        self.log("\n📊 步骤2: 获取音频源详细信息...")
        input_response = self.send_request("GetInputSettings", {
            "inputName": source_name
        })

        if input_response and input_response.get('requestStatus', {}).get('result'):
            input_data = input_response.get('responseData', {})
            input_kind = input_data.get('inputKind', '')
            input_settings = input_data.get('inputSettings', {})

            self.log(f"✅ 音频源类型: {input_kind}")
            self.log("📊 音频源设置:")
            for key, value in input_settings.items():
                self.log(f"   {key}: {value}")

        # 3. 获取所有滤镜
        self.log("\n🎛️ 步骤3: 获取所有滤镜...")
        filter_response = self.send_request("GetSourceFilterList", {
            "sourceName": source_name
        })

        if filter_response and filter_response.get('requestStatus', {}).get('result'):
            filters = filter_response.get('responseData', {}).get('filters', [])

            if not filters:
                self.log("❌ 该音频源没有任何滤镜")
                return

            self.log(f"✅ 找到 {len(filters)} 个滤镜")

            vst_filters = []
            for f in filters:
                filter_name = f.get('filterName', '')
                filter_kind = f.get('filterKind', '')
                filter_enabled = f.get('filterEnabled', False)

                status = "✅" if filter_enabled else "❌"
                self.log(f"   {status} {filter_name} ({filter_kind})")

                if 'vst' in filter_kind.lower():
                    vst_filters.append(filter_name)

            if not vst_filters:
                self.log("❌ 未找到VST滤镜")
                return

            # 4. 深度探测每个VST滤镜
            for vst_filter_name in vst_filters:
                self.log(f"\n🔬 步骤4: 深度探测VST滤镜: {vst_filter_name}")
                self.log("-" * 50)

                # 获取滤镜详细信息
                vst_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": vst_filter_name
                })

                if vst_response and vst_response.get('requestStatus', {}).get('result'):
                    vst_data = vst_response.get('responseData', {})

                    self.log("📊 VST滤镜完整信息:")
                    self.log(f"   滤镜名称: {vst_data.get('filterName', '')}")
                    self.log(f"   滤镜类型: {vst_data.get('filterKind', '')}")
                    self.log(f"   启用状态: {vst_data.get('filterEnabled', False)}")
                    self.log(f"   滤镜索引: {vst_data.get('filterIndex', 0)}")

                    # 最重要的：获取所有VST参数
                    vst_settings = vst_data.get('filterSettings', {})
                    self.log(f"\n🎛️ VST参数 (共 {len(vst_settings)} 个):")
                    self.log("=" * 40)

                    param_count = 0
                    for key, value in vst_settings.items():
                        param_count += 1

                        # 分析参数类型
                        param_type = type(value).__name__

                        if key == 'plugin_path':
                            self.log(f"📁 {param_count:2d}. {key}: {value}")
                        elif key in ['chunk_data', 'chunk_hash']:
                            self.log(f"💾 {param_count:2d}. {key}: {str(value)[:50]}...")
                        else:
                            # 这些是真正的VST参数
                            try:
                                num_value = float(value)
                                self.log(f"🎚️ {param_count:2d}. {key}: {num_value} ({param_type})")

                                # 分析可能的参数用途
                                key_lower = key.lower()
                                if any(word in key_lower for word in ['pitch', 'tune', 'transpose', 'semitone']):
                                    self.log(f"     🎵 -> 可能是音调参数")
                                elif any(word in key_lower for word in ['formant', 'voice', 'vocal']):
                                    self.log(f"     🗣️ -> 可能是声音特征参数")
                                elif any(word in key_lower for word in ['mix', 'wet', 'dry', 'blend']):
                                    self.log(f"     🔀 -> 可能是混合参数")
                                elif any(word in key_lower for word in ['correct', 'strength', 'amount']):
                                    self.log(f"     ⚙️ -> 可能是处理强度参数")

                            except (ValueError, TypeError):
                                self.log(f"📝 {param_count:2d}. {key}: {value} ({param_type})")

                    self.log("=" * 40)
                    self.log(f"🎯 发现 {param_count} 个VST参数")

                    # 5. 尝试获取VST插件的更多信息
                    self.log(f"\n🔍 步骤5: 分析VST插件...")
                    plugin_path = vst_settings.get('plugin_path', '')
                    if plugin_path:
                        import os
                        plugin_name = os.path.basename(plugin_path)
                        self.log(f"📁 插件文件: {plugin_name}")
                        self.log(f"📍 完整路径: {plugin_path}")

                        # 检查是否是Graillon
                        if 'graillon' in plugin_name.lower():
                            self.log("🎉 确认这是Graillon插件！")
                        else:
                            self.log(f"ℹ️ 这是 {plugin_name} 插件")

                else:
                    self.log(f"❌ 无法获取VST滤镜详细信息: {vst_filter_name}")

        else:
            self.log("❌ 获取滤镜列表失败")

        self.log(f"\n🎉 深度探测完成: {source_name}")
        self.log("=" * 60)

    def test_all_apis(self):
        """测试所有相关的OBS API"""
        self.log("🧪 测试所有相关的OBS API...")
        self.log("=" * 60)

        # 测试的API列表
        apis_to_test = [
            ("GetVersion", {}, "获取OBS版本信息"),
            ("GetStats", {}, "获取OBS统计信息"),
            ("GetSceneList", {}, "获取场景列表"),
            ("GetInputList", {}, "获取输入源列表"),
            ("GetSourceFilterList", {"sourceName": self.source_var.get()}, "获取指定源的滤镜列表"),
        ]

        for api_name, params, description in apis_to_test:
            self.log(f"\n🧪 测试API: {api_name}")
            self.log(f"📝 描述: {description}")

            response = self.send_request(api_name, params)

            if response:
                status = response.get('requestStatus', {})
                if status.get('result'):
                    self.log("✅ API调用成功")

                    # 显示关键信息
                    response_data = response.get('responseData', {})
                    if api_name == "GetVersion":
                        self.log(f"   OBS版本: {response_data.get('obsVersion', 'Unknown')}")
                        self.log(f"   WebSocket版本: {response_data.get('obsWebSocketVersion', 'Unknown')}")
                    elif api_name == "GetStats":
                        self.log(f"   CPU使用率: {response_data.get('cpuUsage', 0):.1f}%")
                        self.log(f"   内存使用: {response_data.get('memoryUsage', 0):.1f}MB")
                    elif api_name == "GetInputList":
                        inputs = response_data.get('inputs', [])
                        self.log(f"   找到 {len(inputs)} 个输入源")
                    elif api_name == "GetSourceFilterList":
                        filters = response_data.get('filters', [])
                        self.log(f"   找到 {len(filters)} 个滤镜")
                else:
                    error_code = status.get('code', 0)
                    error_comment = status.get('comment', '')
                    self.log(f"❌ API调用失败: {error_code} - {error_comment}")
            else:
                self.log("❌ 无响应")

        self.log("\n🧪 API测试完成")
        self.log("=" * 60)

    def monitor_parameter_changes(self):
        """监控参数变化"""
        self.log("📡 开始监控参数变化...")
        self.log("请在OBS中手动调整VST插件参数，这里会实时显示变化")
        self.log("=" * 60)

        # 这个功能需要在后台线程中运行
        # 暂时显示提示信息
        self.log("💡 监控功能开发中...")
        self.log("建议手动多次点击'深度探测此音频源'来观察参数变化")

    def export_data(self):
        """导出探测到的数据"""
        self.log("💾 导出功能开发中...")
        self.log("当前所有探测结果已显示在界面中，可以手动复制保存")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = OBSVSTInspector()
    app.run()
