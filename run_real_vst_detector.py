#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 真实VST插件ID检测器启动脚本
连接到OBS WebSocket，获取真实的VST2x插件信息
"""

import sys
import os

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import tkinter
    except ImportError:
        missing_packages.append("tkinter (Python内置，请检查Python安装)")
        
    try:
        import websocket
    except ImportError:
        missing_packages.append("websocket-client")
        
    return missing_packages

def main():
    """主函数"""
    print("🎛️ 启动真实VST插件ID检测器...")
    print("="*60)
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print("❌ 缺少依赖包:")
        for pkg in missing:
            print(f"   • {pkg}")
        print("\n请安装缺少的包:")
        print("pip install websocket-client")
        input("\n按回车键退出...")
        return
    
    try:
        # 导入并运行VST检测器
        from real_vst_plugin_detector import RealVSTPluginDetector
        
        print("✅ 正在启动VST插件ID检测器...")
        print("\n📋 使用说明:")
        print("   1. 确保OBS Studio正在运行")
        print("   2. 确保OBS WebSocket服务器已启用")
        print("   3. 点击'连接OBS'按钮连接")
        print("   4. 选择音频源检测VST插件")
        print("   5. 分析VST插件获取ID和参数信息")
        print("="*60)
        
        # 创建并运行应用程序
        app = RealVSTPluginDetector()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保real_vst_plugin_detector.py文件存在")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
