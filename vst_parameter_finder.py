#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST插件参数发现工具
用于发现和测试VST插件的可控制参数
"""

import obsws_python as obs
import json
import time

class VSTParameterFinder:
    def __init__(self, host="localhost", port=4455, password=""):
        """初始化OBS WebSocket连接"""
        self.client = obs.ReqClient(host=host, port=port, password=password)
        
    def discover_vst_parameters(self, source_name, filter_name):
        """发现VST插件的所有参数"""
        try:
            # 获取滤镜当前设置
            response = self.client.get_source_filter(
                source_name=source_name,
                filter_name=filter_name
            )
            
            filter_settings = response.filter_settings
            print(f"🔍 发现 {filter_name} 的参数:")
            print("=" * 50)
            
            for key, value in filter_settings.items():
                print(f"📋 {key}: {value} ({type(value).__name__})")
            
            return filter_settings
            
        except Exception as e:
            print(f"❌ 发现参数失败: {e}")
            return None
    
    def test_parameter(self, source_name, filter_name, param_name, test_values):
        """测试特定参数的不同值"""
        print(f"🧪 测试参数 {param_name}...")
        
        # 获取原始值
        original_settings = self.discover_vst_parameters(source_name, filter_name)
        original_value = original_settings.get(param_name, "未知")
        print(f"📌 原始值: {original_value}")
        
        # 测试不同值
        for i, test_value in enumerate(test_values):
            try:
                print(f"🎯 测试值 {i+1}: {test_value}")
                
                self.client.set_source_filter_settings(
                    source_name=source_name,
                    filter_name=filter_name,
                    filter_settings={param_name: test_value}
                )
                
                print(f"✅ 设置成功")
                time.sleep(1)  # 等待效果
                
            except Exception as e:
                print(f"❌ 设置失败: {e}")
        
        # 恢复原始值
        try:
            self.client.set_source_filter_settings(
                source_name=source_name,
                filter_name=filter_name,
                filter_settings={param_name: original_value}
            )
            print(f"🔄 已恢复原始值: {original_value}")
        except:
            print("⚠️ 恢复原始值失败")
    
    def batch_test_common_parameters(self, source_name, filter_name):
        """批量测试常见的VST参数名称"""
        
        # 常见参数名称和测试值
        common_params = {
            # 音调相关
            "pitch": [-12.0, -6.0, 0.0, 6.0, 12.0],
            "Pitch": [-12.0, -6.0, 0.0, 6.0, 12.0],
            "correction": [0.0, 0.5, 1.0],
            "Correction": [0.0, 0.5, 1.0],
            
            # 失真相关
            "drive": [0.0, 0.3, 0.5, 0.8, 1.0],
            "Drive": [0.0, 0.3, 0.5, 0.8, 1.0],
            "gain": [0.0, 0.3, 0.5, 0.8, 1.0],
            "Gain": [0.0, 0.3, 0.5, 0.8, 1.0],
            "tone": [0.0, 0.3, 0.5, 0.8, 1.0],
            "Tone": [0.0, 0.3, 0.5, 0.8, 1.0],
            
            # 混响相关
            "roomsize": [0.0, 0.3, 0.6, 1.0],
            "RoomSize": [0.0, 0.3, 0.6, 1.0],
            "mix": [0.0, 0.2, 0.5, 0.8],
            "Mix": [0.0, 0.2, 0.5, 0.8],
            "wet": [0.0, 0.2, 0.5, 0.8],
            "Wet": [0.0, 0.2, 0.5, 0.8],
            "damping": [0.0, 0.5, 1.0],
            "Damping": [0.0, 0.5, 1.0],
            
            # 通用参数
            "bypass": [True, False],
            "Bypass": [True, False],
            "enabled": [True, False],
            "Enabled": [True, False],
        }
        
        print(f"🔍 开始批量测试 {filter_name} 的常见参数...")
        print("=" * 60)
        
        # 先发现现有参数
        existing_params = self.discover_vst_parameters(source_name, filter_name)
        if not existing_params:
            return
        
        print("\n🧪 开始参数测试...")
        print("=" * 60)
        
        successful_params = {}
        
        for param_name, test_values in common_params.items():
            if param_name in existing_params:
                print(f"\n✅ 发现参数: {param_name}")
                try:
                    self.test_parameter(source_name, filter_name, param_name, test_values[:2])  # 只测试前两个值
                    successful_params[param_name] = {
                        "original_value": existing_params[param_name],
                        "test_values": test_values,
                        "type": type(existing_params[param_name]).__name__
                    }
                except Exception as e:
                    print(f"❌ 测试失败: {e}")
            else:
                # 尝试设置不存在的参数
                try:
                    self.client.set_source_filter_settings(
                        source_name=source_name,
                        filter_name=filter_name,
                        filter_settings={param_name: test_values[0]}
                    )
                    print(f"🆕 新参数可能有效: {param_name}")
                    successful_params[param_name] = {
                        "original_value": None,
                        "test_values": test_values,
                        "type": "unknown"
                    }
                except:
                    pass  # 静默忽略失败的参数
        
        # 输出总结
        print("\n📊 测试总结:")
        print("=" * 60)
        for param, info in successful_params.items():
            print(f"✅ {param}: {info['type']} - 原始值: {info['original_value']}")
        
        return successful_params
    
    def generate_control_code(self, source_name, filter_name, successful_params):
        """生成控制代码模板"""
        code_template = f'''
# {filter_name} 控制代码模板
def control_{filter_name.lower().replace('-', '_').replace(' ', '_')}(controller, source_name="{source_name}", filter_name="{filter_name}"):
    """控制 {filter_name} 插件参数"""
    import random
    
'''
        
        for param, info in successful_params.items():
            if info['type'] in ['int', 'float']:
                code_template += f'''    # {param} 参数控制
    {param}_value = random.uniform(0.0, 1.0)  # 根据需要调整范围
    controller.set_vst_parameter(source_name, filter_name, "{param}", {param}_value)
    
'''
            elif info['type'] == 'bool':
                code_template += f'''    # {param} 开关控制
    {param}_value = random.choice([True, False])
    controller.set_vst_parameter(source_name, filter_name, "{param}", {param}_value)
    
'''
        
        print("\n💻 生成的控制代码:")
        print("=" * 60)
        print(code_template)
        
        return code_template

def main():
    """主函数 - 使用示例"""
    finder = VSTParameterFinder(password="your_websocket_password")
    
    # 配置你的音频源和VST滤镜名称
    source_name = "麦克风"  # 替换为你的音频源名称
    filter_name = "Graillon"  # 替换为你的VST滤镜名称
    
    try:
        print(f"🎛️ 开始分析 {source_name} 上的 {filter_name} 插件")
        print("=" * 60)
        
        # 1. 发现所有参数
        all_params = finder.discover_vst_parameters(source_name, filter_name)
        
        if all_params:
            # 2. 批量测试常见参数
            successful_params = finder.batch_test_common_parameters(source_name, filter_name)
            
            # 3. 生成控制代码
            if successful_params:
                finder.generate_control_code(source_name, filter_name, successful_params)
            
            print(f"\n🎉 分析完成! 发现 {len(successful_params)} 个可控制参数")
        else:
            print("❌ 未能获取插件参数，请检查音频源和滤镜名称")
            
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
