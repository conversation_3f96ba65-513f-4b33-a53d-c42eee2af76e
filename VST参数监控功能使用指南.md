# 🧪 VST参数测试器 - 实时监控功能使用指南

## 📡 监控功能改进说明

### 🔧 主要改进

1. **无需预探测**: 不再需要先运行"探测参数"功能
2. **全参数监控**: 自动监控所有VST参数的变化
3. **高精度检测**: 数值变化检测精度提升至0.001
4. **快速响应**: 监控间隔从1秒缩短至0.5秒
5. **错误恢复**: 改进的错误处理和自动重连机制

### 🆕 新增功能

- **🔄 手动刷新**: 立即查看当前所有参数状态
- **📊 详细日志**: 显示参数的精确数值变化
- **📋 初始状态**: 监控开始时显示所有参数的初始值

## 📋 使用步骤

### 1. 基本设置
```
1. 启动 vst_parameter_tester.py
2. 点击"连接OBS"
3. 确认连接成功 (显示绿色✅)
```

### 2. 配置VST信息
```
1. 在"音频源"框中输入您的音频源名称 (如: "媒体源")
2. 在"VST滤镜"框中输入VST滤镜名称 (如: "VST 2.x 插件")
3. 点击"🔄 手动刷新"验证配置是否正确
```

### 3. 开始监控
```
1. 勾选"📡 实时监控"复选框
2. 程序会显示所有参数的初始值
3. 在OBS中调节VST插件参数
4. 观察程序中的实时变化日志
```

## 🎯 监控日志说明

### 📋 初始参数显示
```
[时间] 📋 初始参数: pitch = 2.484
[时间] 📋 初始参数: formant = 100.0
[时间] 📋 初始参数: mix = 100.0
```

### 📊 数值参数变化
```
[时间] 📊 参数变化: pitch = 2.484 → 5.123
[时间] 📊 参数变化: formant = 100.000 → 125.500
```

### 📝 非数值参数变化
```
[时间] 📝 参数变化: bypass = false → true
```

## 🔧 故障排除

### ❌ 监控没有检测到变化

**可能原因:**
1. 音频源名称不正确
2. VST滤镜名称不正确
3. VST插件没有实际改变参数值
4. OBS WebSocket连接问题

**解决方法:**
1. 使用"🔄 手动刷新"验证连接
2. 检查OBS中的实际源名称和滤镜名称
3. 在OBS中手动调节VST参数，确认界面有变化
4. 重新连接OBS WebSocket

### ⚠️ 监控频繁出错

**可能原因:**
1. OBS WebSocket连接不稳定
2. VST插件响应缓慢
3. 系统资源不足

**解决方法:**
1. 重启OBS和程序
2. 检查OBS WebSocket插件是否正常运行
3. 关闭其他占用资源的程序

## 💡 使用技巧

### 🎯 精确测试
```
1. 先使用"🔄 手动刷新"查看当前状态
2. 开启"📡 实时监控"
3. 在OBS中进行小幅度调节
4. 观察程序中的精确数值变化
```

### 🧪 参数探索
```
1. 使用"🔍 探测参数"发现所有可用参数
2. 结合"📡 实时监控"观察参数效果
3. 使用"🧠 智能测试"自动测试发现的参数
```

### 📊 效果验证
```
1. 记录参数变化前的数值
2. 在OBS中调节参数
3. 确认程序检测到变化
4. 验证音频效果是否对应
```

## 🚀 高级功能

### 🧠 智能测试配合监控
```
1. 先开启"📡 实时监控"
2. 运行"🧠 智能测试"
3. 观察每个测试值的实际设置情况
4. 验证参数控制的有效性
```

### ⚡ 极端值测试配合监控
```
1. 开启监控查看当前参数值
2. 运行"⚡ 极端值测试"
3. 观察参数是否真的达到极端值
4. 验证音频效果的明显变化
```

## 📞 技术支持

如果监控功能仍然无法正常工作，请检查:

1. **OBS WebSocket**: 确保OBS WebSocket插件已安装并启用
2. **端口设置**: 默认端口4455是否被占用
3. **权限问题**: 程序是否有足够权限访问OBS
4. **VST插件**: VST插件是否正常加载和工作

---

*最后更新: 2025-07-28*
