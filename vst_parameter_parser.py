#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ VST参数解析器
专门用于解析VST插件的chunk_data，获取具体的数值参数
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
import base64
import struct
from datetime import datetime

class VSTParameterParser:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ VST参数解析器 - 获取具体数值参数")
        self.root.geometry("1000x700")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 当前VST数据
        self.current_vst_data = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST选择区域
        vst_frame = ttk.LabelFrame(main_frame, text="🎛️ VST插件选择", padding="10")
        vst_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(vst_frame, text="音频源:").pack(side=tk.LEFT)
        self.source_combo = ttk.Combobox(vst_frame, width=20, state="readonly")
        self.source_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(vst_frame, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_combo = ttk.Combobox(vst_frame, width=20, state="readonly")
        self.filter_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        self.refresh_btn = ttk.Button(vst_frame, text="🔄 刷新", command=self.refresh_data, state="disabled")
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.parse_btn = ttk.Button(vst_frame, text="🔍 解析参数", command=self.parse_vst_parameters, state="disabled")
        self.parse_btn.pack(side=tk.LEFT)
        
        # 创建Notebook用于显示不同信息
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 参数监控标签页
        self.setup_parameter_monitor_tab()
        
        # 原始数据标签页
        self.setup_raw_data_tab()
        
        # 日志标签页
        self.setup_log_tab()
        
    def setup_parameter_monitor_tab(self):
        """设置参数监控标签页"""
        monitor_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(monitor_frame, text="📊 参数监控")
        
        # 控制区域
        control_frame = ttk.LabelFrame(monitor_frame, text="🎚️ 参数控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.monitor_enabled = tk.BooleanVar()
        monitor_cb = ttk.Checkbutton(control_frame, text="启用实时监控", variable=self.monitor_enabled, 
                                   command=self.toggle_monitoring)
        monitor_cb.pack(side=tk.LEFT)
        
        ttk.Label(control_frame, text="监控间隔:").pack(side=tk.LEFT, padx=(20, 5))
        self.interval_var = tk.StringVar(value="1000")
        interval_spin = ttk.Spinbox(control_frame, from_=500, to=5000, increment=500, 
                                  textvariable=self.interval_var, width=10)
        interval_spin.pack(side=tk.LEFT)
        ttk.Label(control_frame, text="ms").pack(side=tk.LEFT, padx=(2, 0))
        
        # 参数显示区域
        self.param_text = scrolledtext.ScrolledText(monitor_frame, height=25, wrap=tk.WORD, 
                                                   font=('Consolas', 10))
        self.param_text.pack(fill=tk.BOTH, expand=True)
        
        # 监控定时器
        self.monitor_timer = None
        
    def setup_raw_data_tab(self):
        """设置原始数据标签页"""
        raw_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(raw_frame, text="📄 原始数据")
        
        self.raw_text = scrolledtext.ScrolledText(raw_frame, height=30, wrap=tk.WORD, 
                                                 font=('Consolas', 9))
        self.raw_text.pack(fill=tk.BOTH, expand=True)
        
    def setup_log_tab(self):
        """设置日志标签页"""
        log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(log_frame, text="📝 日志")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=30, wrap=tk.WORD, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🎛️ VST参数解析器启动")
        self.log("请先连接到OBS WebSocket")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_msg)
        self.log_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.refresh_btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.refresh_data()
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.refresh_btn.config(state="disabled")
        self.parse_btn.config(state="disabled")
        
        if self.monitor_timer:
            self.root.after_cancel(self.monitor_timer)
            self.monitor_timer = None
            
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
            
    def refresh_data(self):
        """刷新数据"""
        if not self.is_connected:
            return
            
        self.log("🔄 正在刷新音频源和VST滤镜...")
        
        # 获取输入源列表
        response = self.send_request("GetInputList")
        if response and response.get('requestStatus', {}).get('result'):
            inputs = response.get('responseData', {}).get('inputs', [])
            
            # 过滤音频源
            audio_sources = []
            for item in inputs:
                input_name = item.get("inputName")
                input_kind = item.get("inputKind")
                if not input_name:
                    continue
                
                has_audio_tracks = item.get("audioTracks") is not None and len(item.get("audioTracks")) > 0
                is_dedicated_audio = input_kind in [
                    "wasapi_input_capture", "wasapi_output_capture",
                    "coreaudio_input_capture", "coreaudio_output_capture", 
                    "pulse_input_capture", "pulse_output_capture",
                    "jack_input_client", "alsa_input_capture"
                ]
                is_audio_capable = has_audio_tracks or is_dedicated_audio
                is_media_source = (input_kind == 'ffmpeg_source' or input_kind == 'vlc_source')
                
                if is_audio_capable or is_media_source:
                    audio_sources.append(input_name)
                    
            self.source_combo['values'] = audio_sources
            if audio_sources:
                self.source_combo.set(audio_sources[0])
                self.refresh_filters()
                
        self.log(f"✅ 找到 {len(audio_sources)} 个音频源")
        
    def refresh_filters(self):
        """刷新VST滤镜列表"""
        source_name = self.source_combo.get()
        if not source_name:
            return
            
        response = self.send_request("GetSourceFilterList", {"sourceName": source_name})
        if response and response.get('requestStatus', {}).get('result'):
            filters = response.get('responseData', {}).get('filters', [])
            
            vst_filters = []
            for filter_item in filters:
                filter_name = filter_item.get('filterName', '')
                filter_kind = filter_item.get('filterKind', '')
                
                if 'vst' in filter_kind.lower():
                    vst_filters.append(filter_name)
                    
            self.filter_combo['values'] = vst_filters
            if vst_filters:
                self.filter_combo.set(vst_filters[0])
                self.parse_btn.config(state="normal")
                
            self.log(f"✅ 找到 {len(vst_filters)} 个VST滤镜")
            
    def parse_vst_parameters(self):
        """解析VST参数"""
        source_name = self.source_combo.get()
        filter_name = self.filter_combo.get()
        
        if not source_name or not filter_name:
            messagebox.showwarning("警告", "请先选择音频源和VST滤镜")
            return
            
        self.log(f"🔍 正在解析VST滤镜: {filter_name}")
        
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            self.current_vst_data = filter_data
            
            # 显示原始数据
            self.show_raw_data(filter_data)
            
            # 尝试解析参数
            self.analyze_vst_chunk(filter_data)
            
            self.log("✅ VST参数解析完成")
        else:
            self.log("❌ 获取VST滤镜信息失败")
            
    def show_raw_data(self, data):
        """显示原始数据"""
        self.raw_text.delete(1.0, tk.END)
        formatted_json = json.dumps(data, indent=4, ensure_ascii=False)
        self.raw_text.insert(tk.END, formatted_json)
        
    def analyze_vst_chunk(self, filter_data):
        """分析VST chunk数据"""
        settings = filter_data.get('filterSettings', {})
        chunk_data = settings.get('chunk_data', '')
        plugin_path = settings.get('plugin_path', '')
        
        self.param_text.delete(1.0, tk.END)
        
        analysis = f"🎛️ VST插件参数分析\n"
        analysis += "=" * 50 + "\n\n"
        analysis += f"插件路径: {plugin_path}\n"
        analysis += f"滤镜名称: {filter_data.get('filterName', 'Unknown')}\n"
        analysis += f"启用状态: {'✅ 启用' if filter_data.get('filterEnabled') else '❌ 禁用'}\n\n"
        
        if chunk_data:
            analysis += f"📊 Chunk数据分析:\n"
            analysis += f"数据长度: {len(chunk_data)} 字符\n"
            analysis += f"数据类型: Base64编码的二进制数据\n\n"
            
            # 尝试解码chunk数据
            try:
                # 解码base64数据
                decoded_data = base64.b64decode(chunk_data)
                analysis += f"解码后长度: {len(decoded_data)} 字节\n\n"
                
                # 分析数据结构
                analysis += "🔍 数据结构分析:\n"
                analysis += f"前16字节 (hex): {decoded_data[:16].hex()}\n"
                
                # 尝试解析为float数组
                if len(decoded_data) >= 4:
                    analysis += "\n📈 可能的参数值 (解析为float):\n"
                    float_count = min(20, len(decoded_data) // 4)  # 最多显示20个float值
                    for i in range(float_count):
                        try:
                            offset = i * 4
                            float_val = struct.unpack('<f', decoded_data[offset:offset+4])[0]
                            analysis += f"  参数 {i:2d}: {float_val:8.3f}\n"
                        except:
                            break
                            
                # 检查是否包含特定插件的特征值
                analysis += "\n🎯 插件特征分析:\n"
                if b'Graillon' in decoded_data or b'Auburn' in decoded_data:
                    analysis += "  ✅ 检测到Graillon插件特征\n"
                elif b'TSE' in decoded_data or b'808' in decoded_data:
                    analysis += "  ✅ 检测到TSE808插件特征\n"
                elif b'TAL' in decoded_data or b'Reverb' in decoded_data:
                    analysis += "  ✅ 检测到TAL混响插件特征\n"
                else:
                    analysis += "  ℹ️ 未检测到已知插件特征\n"
                    
            except Exception as e:
                analysis += f"❌ 解码失败: {e}\n"
                
        else:
            analysis += "⚠️ 未找到chunk_data\n"
            
        analysis += "\n" + "=" * 50 + "\n"
        analysis += "💡 提示: VST插件参数通常以二进制格式存储在chunk_data中\n"
        analysis += "要获取具体参数值，需要启用实时监控功能\n"
        
        self.param_text.insert(tk.END, analysis)
        
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitor_enabled.get():
            self.start_monitoring()
        else:
            self.stop_monitoring()
            
    def start_monitoring(self):
        """开始监控"""
        if not self.current_vst_data:
            messagebox.showwarning("警告", "请先解析VST参数")
            self.monitor_enabled.set(False)
            return
            
        self.log("📡 开始实时监控VST参数变化...")
        self.monitor_parameters()
        
    def stop_monitoring(self):
        """停止监控"""
        if self.monitor_timer:
            self.root.after_cancel(self.monitor_timer)
            self.monitor_timer = None
        self.log("📡 停止实时监控")
        
    def monitor_parameters(self):
        """监控参数变化"""
        if not self.monitor_enabled.get():
            return
            
        source_name = self.source_combo.get()
        filter_name = self.filter_combo.get()
        
        if source_name and filter_name:
            response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            }, timeout=1)
            
            if response and response.get('requestStatus', {}).get('result'):
                filter_data = response.get('responseData', {})
                current_chunk = filter_data.get('filterSettings', {}).get('chunk_data', '')
                
                # 检查是否有变化
                old_chunk = self.current_vst_data.get('filterSettings', {}).get('chunk_data', '')
                if current_chunk != old_chunk:
                    self.current_vst_data = filter_data
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    # 在参数监控区域显示变化
                    change_info = f"\n[{timestamp}] 🔄 检测到参数变化\n"
                    change_info += f"新chunk长度: {len(current_chunk)}\n"
                    
                    # 尝试解析变化的参数
                    if current_chunk:
                        try:
                            decoded_data = base64.b64decode(current_chunk)
                            change_info += "📊 当前参数值:\n"
                            float_count = min(10, len(decoded_data) // 4)
                            for i in range(float_count):
                                offset = i * 4
                                float_val = struct.unpack('<f', decoded_data[offset:offset+4])[0]
                                change_info += f"  参数 {i}: {float_val:.3f}\n"
                        except:
                            change_info += "❌ 解析失败\n"
                    
                    self.param_text.insert(tk.END, change_info)
                    self.param_text.see(tk.END)
                    
        # 设置下次监控
        interval = int(self.interval_var.get())
        self.monitor_timer = self.root.after(interval, self.monitor_parameters)
        
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTParameterParser()
    app.run()
