# 🎛️ VST插件ID检测器使用说明

## 📋 工具概述

VST插件ID检测器是一个专门用于检测OBS中VST2x插件ID和参数信息的可视化工具。它可以帮助您：

- 🔍 检测OBS中的VST2x滤镜插件
- 📊 获取插件的详细ID和参数信息
- 📡 实时监控参数变化
- 💾 导出检测结果为JSON文件

## 🚀 快速开始

### 1. 启动工具

```bash
# 方法1：直接运行检测器
python vst_plugin_id_detector.py

# 方法2：使用启动脚本
python run_vst_detector.py
```

### 2. 连接OBS

1. **启动OBS Studio**
2. **在工具中点击"🔗 连接OBS"按钮**
3. **等待连接成功提示**

### 3. 选择音频源

在顶部的"音频源"下拉菜单中选择您要检测的音频源（如：麦克风、桌面音频等）

## 🎯 主要功能

### 🔍 插件检测

#### 自动检测
- 点击"🚀 一键检测所有VST插件"按钮
- 工具会自动扫描选定音频源的所有VST2x滤镜
- 检测结果显示在左侧插件列表中

#### 手动检测
- 点击"🔍 检测插件"按钮
- 针对当前音频源进行插件检测

### 📊 插件信息查看

选择插件后，右侧会显示四个标签页：

#### 🔍 插件信息
- **插件名称**：在OBS中的滤镜名称
- **插件ID**：VST插件的唯一标识符
- **滤镜类型**：通常为"vst_filter"
- **参数数量**：插件包含的参数总数
- **插件路径**：VST插件文件的完整路径

#### 📊 参数列表
显示插件的所有参数信息：
- **参数名称**：参数的标识名称
- **参数ID**：参数的唯一ID
- **当前值**：参数的当前数值
- **类型**：参数类型（float/bool/string）
- **范围**：参数的有效取值范围
- **描述**：参数的功能说明

#### 📡 实时监控
- 启用实时监控可以观察参数的动态变化
- 可设置监控更新间隔（100-5000ms）
- 显示参数变化的时间戳记录

#### 📝 日志
- 显示工具的操作日志
- 可以保存日志到文件
- 支持自动滚动显示

## 🎛️ 针对您的三个VST插件

根据您提到的三个VST插件，工具会检测到：

### 1. Auburn Sounds Graillon 3-64 🎵
```json
{
  "plugin_id": "Auburn Sounds Graillon 3-64",
  "parameters": {
    "pitch": "音调偏移（半音）",
    "formant": "共振峰调节", 
    "mix": "干湿混合比例",
    "bypass": "旁路开关"
  }
}
```

### 2. TSE_808_2.0_x64 🔥
```json
{
  "plugin_id": "TSE_808_2.0_x64",
  "parameters": {
    "drive": "驱动强度",
    "tone": "音色调节",
    "level": "输出电平",
    "enabled": "启用状态"
  }
}
```

### 3. TAL-Reverb-4-64 🌊
```json
{
  "plugin_id": "TAL-Reverb-4-64", 
  "parameters": {
    "roomsize": "房间大小",
    "damping": "阻尼系数",
    "mix": "混响混合",
    "predelay": "预延迟"
  }
}
```

## 💾 导出功能

### 导出检测结果
1. 完成插件检测后，点击"💾 导出检测结果"
2. 选择保存位置和文件名
3. 结果将保存为JSON格式，包含：
   - 检测时间戳
   - 音频源名称
   - 所有插件的完整信息

### 导出文件格式示例
```json
{
  "timestamp": "2025-07-28 15:30:45",
  "source": "麦克风",
  "plugins": {
    "Graillon音调": {
      "plugin_id": "Auburn Sounds Graillon 3-64",
      "filter_type": "vst_filter",
      "plugin_path": "C:/Program Files/VstPlugins/Auburn Sounds/Graillon 3-64.dll",
      "parameters": {
        "pitch": {
          "value": 0.0,
          "type": "float", 
          "range": [-12.0, 12.0],
          "description": "音调偏移（半音）"
        }
      }
    }
  }
}
```

## 🔧 高级功能

### 参数过滤
- **全部参数**：显示所有参数
- **数值参数**：只显示float类型参数
- **布尔参数**：只显示bool类型参数
- **字符串参数**：只显示string类型参数

### 实时监控设置
- **更新间隔**：100ms - 5000ms可调
- **自动清空**：防止监控数据过多
- **时间戳记录**：精确到毫秒

### 日志管理
- **自动滚动**：新日志自动显示
- **保存日志**：导出为文本文件
- **清空日志**：重置日志显示

## ⚠️ 注意事项

### 使用前准备
1. **确保OBS Studio正在运行**
2. **确保已添加VST2x滤镜到音频源**
3. **确保VST插件正常工作**

### 兼容性说明
- 支持VST 2.x格式插件
- 需要OBS Studio 27.0+版本
- 需要Python 3.7+和PyQt5

### 故障排除
- **连接失败**：检查OBS WebSocket设置
- **检测不到插件**：确认滤镜已正确添加
- **参数显示异常**：尝试刷新参数列表

## 🎯 实际应用

使用这个工具检测到的插件ID和参数信息，您可以：

1. **在主程序中精确控制VST参数**
2. **编写自动化脚本**
3. **创建参数预设配置**
4. **调试VST插件问题**

## 📞 技术支持

如果在使用过程中遇到问题：
1. 查看日志标签页的错误信息
2. 确认OBS和VST插件设置
3. 检查Python环境和依赖包
