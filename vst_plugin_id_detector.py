#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ VST插件ID检测器
专门用于检测OBS中VST2x插件的ID和参数信息的可视化工具
"""

import sys
import json
import time
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QPushButton, QLabel, QComboBox, QTextEdit, 
                            QGroupBox, QGridLayout, QTableWidget, QTableWidgetItem,
                            QHeaderView, QSplitter, QTabWidget, QLineEdit, QSpinBox,
                            QCheckBox, QProgressBar, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette

class VST_Plugin_ID_Detector(QMainWindow):
    """VST插件ID检测器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎛️ VST插件ID检测器 - OBS VST2x插件分析工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 模拟OBS连接状态
        self.is_connected = False
        self.obs_client = None
        
        # 检测到的插件数据
        self.detected_plugins = {}
        self.current_plugin_data = {}
        
        # 设置样式
        self.setup_styles()
        
        # 初始化UI
        self.setup_ui()
        
        # 模拟数据（实际使用时会连接真实OBS）
        self.setup_mock_data()
        
    def setup_styles(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #3c3c3c;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #00ff88;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
            QComboBox, QLineEdit, QSpinBox {
                padding: 5px;
                border: 2px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
            }
            QTextEdit {
                border: 2px solid #555555;
                border-radius: 4px;
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            QTableWidget {
                border: 2px solid #555555;
                border-radius: 4px;
                background-color: #2b2b2b;
                alternate-background-color: #3c3c3c;
                gridline-color: #555555;
            }
            QTableWidget::item {
                padding: 5px;
                color: #ffffff;
            }
            QTableWidget::item:selected {
                background-color: #4CAF50;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 2px solid #555555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #404040;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
            }
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #404040;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)
        
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部控制区域
        self.setup_control_area(main_layout)
        
        # 主要内容区域（使用分割器）
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：插件列表和控制
        left_widget = self.setup_left_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：详细信息显示
        right_widget = self.setup_right_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
        
        # 底部状态栏
        self.setup_status_bar(main_layout)
        
    def setup_control_area(self, parent_layout):
        """设置顶部控制区域"""
        control_group = QGroupBox("🔧 连接和控制")
        control_layout = QHBoxLayout(control_group)
        
        # OBS连接状态
        self.connection_label = QLabel("❌ 未连接到OBS")
        self.connection_label.setStyleSheet("color: #ff4444; font-weight: bold;")
        control_layout.addWidget(self.connection_label)
        
        # 连接按钮
        self.connect_btn = QPushButton("🔗 连接OBS")
        self.connect_btn.clicked.connect(self.toggle_obs_connection)
        control_layout.addWidget(self.connect_btn)
        
        control_layout.addStretch()
        
        # 音频源选择
        control_layout.addWidget(QLabel("音频源:"))
        self.source_combo = QComboBox()
        self.source_combo.addItems(["麦克风", "桌面音频", "音频输入捕获", "媒体源"])
        self.source_combo.currentTextChanged.connect(self.on_source_changed)
        control_layout.addWidget(self.source_combo)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_plugins)
        self.refresh_btn.setEnabled(False)
        control_layout.addWidget(self.refresh_btn)
        
        parent_layout.addWidget(control_group)
        
    def setup_left_panel(self):
        """设置左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # VST插件列表
        plugin_group = QGroupBox("🎛️ 检测到的VST2x插件")
        plugin_layout = QVBoxLayout(plugin_group)
        
        # 插件列表表格
        self.plugin_table = QTableWidget()
        self.plugin_table.setColumnCount(3)
        self.plugin_table.setHorizontalHeaderLabels(["插件名称", "类型", "状态"])
        self.plugin_table.horizontalHeader().setStretchLastSection(True)
        self.plugin_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.plugin_table.itemSelectionChanged.connect(self.on_plugin_selected)
        plugin_layout.addWidget(self.plugin_table)
        
        # 插件操作按钮
        plugin_btn_layout = QHBoxLayout()
        
        self.detect_btn = QPushButton("🔍 检测插件")
        self.detect_btn.clicked.connect(self.detect_plugins)
        self.detect_btn.setEnabled(False)
        plugin_btn_layout.addWidget(self.detect_btn)
        
        self.analyze_btn = QPushButton("📊 分析参数")
        self.analyze_btn.clicked.connect(self.analyze_selected_plugin)
        self.analyze_btn.setEnabled(False)
        plugin_btn_layout.addWidget(self.analyze_btn)
        
        plugin_layout.addLayout(plugin_btn_layout)
        left_layout.addWidget(plugin_group)
        
        # 快速操作
        quick_group = QGroupBox("⚡ 快速操作")
        quick_layout = QVBoxLayout(quick_group)
        
        self.auto_detect_btn = QPushButton("🚀 一键检测所有VST插件")
        self.auto_detect_btn.clicked.connect(self.auto_detect_all_plugins)
        self.auto_detect_btn.setEnabled(False)
        quick_layout.addWidget(self.auto_detect_btn)
        
        self.export_btn = QPushButton("💾 导出检测结果")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        quick_layout.addWidget(self.export_btn)
        
        left_layout.addWidget(quick_group)
        
        return left_widget
        
    def setup_right_panel(self):
        """设置右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 使用标签页显示不同信息
        self.info_tabs = QTabWidget()
        
        # 插件信息标签页
        self.setup_plugin_info_tab()
        
        # 参数列表标签页
        self.setup_parameters_tab()
        
        # 实时监控标签页
        self.setup_monitor_tab()
        
        # 日志标签页
        self.setup_log_tab()
        
        right_layout.addWidget(self.info_tabs)
        
        return right_widget

    def setup_plugin_info_tab(self):
        """设置插件信息标签页"""
        plugin_info_widget = QWidget()
        plugin_info_layout = QVBoxLayout(plugin_info_widget)

        # 插件基本信息
        info_group = QGroupBox("📋 插件基本信息")
        info_layout = QGridLayout(info_group)

        # 信息显示标签
        info_layout.addWidget(QLabel("插件名称:"), 0, 0)
        self.plugin_name_label = QLabel("未选择插件")
        self.plugin_name_label.setStyleSheet("color: #00ff88; font-weight: bold;")
        info_layout.addWidget(self.plugin_name_label, 0, 1)

        info_layout.addWidget(QLabel("插件ID:"), 1, 0)
        self.plugin_id_label = QLabel("-")
        self.plugin_id_label.setStyleSheet("color: #ffaa00; font-family: monospace;")
        info_layout.addWidget(self.plugin_id_label, 1, 1)

        info_layout.addWidget(QLabel("滤镜类型:"), 2, 0)
        self.filter_type_label = QLabel("-")
        info_layout.addWidget(self.filter_type_label, 2, 1)

        info_layout.addWidget(QLabel("参数数量:"), 3, 0)
        self.param_count_label = QLabel("-")
        info_layout.addWidget(self.param_count_label, 3, 1)

        info_layout.addWidget(QLabel("插件路径:"), 4, 0)
        self.plugin_path_label = QLabel("-")
        self.plugin_path_label.setWordWrap(True)
        self.plugin_path_label.setStyleSheet("color: #aaaaaa; font-size: 10px;")
        info_layout.addWidget(self.plugin_path_label, 4, 1)

        plugin_info_layout.addWidget(info_group)

        # 插件详细信息文本
        details_group = QGroupBox("📄 详细信息")
        details_layout = QVBoxLayout(details_group)

        self.plugin_details_text = QTextEdit()
        self.plugin_details_text.setMaximumHeight(200)
        self.plugin_details_text.setPlainText("请选择一个插件查看详细信息...")
        details_layout.addWidget(self.plugin_details_text)

        plugin_info_layout.addWidget(details_group)

        self.info_tabs.addTab(plugin_info_widget, "🔍 插件信息")

    def setup_parameters_tab(self):
        """设置参数列表标签页"""
        params_widget = QWidget()
        params_layout = QVBoxLayout(params_widget)

        # 参数控制区域
        param_control_group = QGroupBox("🎚️ 参数控制")
        param_control_layout = QHBoxLayout(param_control_group)

        param_control_layout.addWidget(QLabel("参数过滤:"))
        self.param_filter_combo = QComboBox()
        self.param_filter_combo.addItems(["全部参数", "数值参数", "布尔参数", "字符串参数"])
        self.param_filter_combo.currentTextChanged.connect(self.filter_parameters)
        param_control_layout.addWidget(self.param_filter_combo)

        param_control_layout.addStretch()

        self.refresh_params_btn = QPushButton("🔄 刷新参数")
        self.refresh_params_btn.clicked.connect(self.refresh_parameters)
        self.refresh_params_btn.setEnabled(False)
        param_control_layout.addWidget(self.refresh_params_btn)

        params_layout.addWidget(param_control_group)

        # 参数表格
        self.params_table = QTableWidget()
        self.params_table.setColumnCount(6)
        self.params_table.setHorizontalHeaderLabels([
            "参数名称", "参数ID", "当前值", "类型", "范围", "描述"
        ])

        # 设置列宽
        header = self.params_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 150)  # 参数名称
        header.resizeSection(1, 100)  # 参数ID
        header.resizeSection(2, 80)   # 当前值
        header.resizeSection(3, 80)   # 类型
        header.resizeSection(4, 120)  # 范围

        self.params_table.setAlternatingRowColors(True)
        self.params_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.params_table.itemDoubleClicked.connect(self.on_parameter_double_clicked)

        params_layout.addWidget(self.params_table)

        self.info_tabs.addTab(params_widget, "📊 参数列表")

    def setup_monitor_tab(self):
        """设置实时监控标签页"""
        monitor_widget = QWidget()
        monitor_layout = QVBoxLayout(monitor_widget)

        # 监控控制
        monitor_control_group = QGroupBox("📡 实时监控控制")
        monitor_control_layout = QHBoxLayout(monitor_control_group)

        self.monitor_enabled_cb = QCheckBox("启用实时监控")
        self.monitor_enabled_cb.stateChanged.connect(self.toggle_monitoring)
        monitor_control_layout.addWidget(self.monitor_enabled_cb)

        monitor_control_layout.addWidget(QLabel("更新间隔:"))
        self.monitor_interval_spin = QSpinBox()
        self.monitor_interval_spin.setRange(100, 5000)
        self.monitor_interval_spin.setValue(1000)
        self.monitor_interval_spin.setSuffix(" ms")
        monitor_control_layout.addWidget(self.monitor_interval_spin)

        monitor_control_layout.addStretch()

        self.clear_monitor_btn = QPushButton("🗑️ 清空监控")
        self.clear_monitor_btn.clicked.connect(self.clear_monitor_data)
        monitor_control_layout.addWidget(self.clear_monitor_btn)

        monitor_layout.addWidget(monitor_control_group)

        # 监控数据显示
        self.monitor_text = QTextEdit()
        self.monitor_text.setPlainText("实时监控未启动...\n\n启用监控后，这里将显示参数变化的实时信息。")
        monitor_layout.addWidget(self.monitor_text)

        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitor_data)

        self.info_tabs.addTab(monitor_widget, "📡 实时监控")

    def setup_log_tab(self):
        """设置日志标签页"""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)

        # 日志控制
        log_control_group = QGroupBox("📝 日志控制")
        log_control_layout = QHBoxLayout(log_control_group)

        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        log_control_layout.addWidget(self.auto_scroll_cb)

        log_control_layout.addStretch()

        self.clear_log_btn = QPushButton("🗑️ 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(self.clear_log_btn)

        self.save_log_btn = QPushButton("💾 保存日志")
        self.save_log_btn.clicked.connect(self.save_log)
        log_control_layout.addWidget(self.save_log_btn)

        log_layout.addWidget(log_control_group)

        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setPlainText("🎛️ VST插件ID检测器启动\n" + "="*50 + "\n")
        log_layout.addWidget(self.log_text)

        self.info_tabs.addTab(log_widget, "📝 日志")

    def setup_status_bar(self, parent_layout):
        """设置底部状态栏"""
        status_group = QGroupBox("📊 状态信息")
        status_layout = QHBoxLayout(status_group)

        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)

        # 统计信息
        self.stats_label = QLabel("插件: 0 | 参数: 0")
        self.stats_label.setStyleSheet("color: #00ff88;")
        status_layout.addWidget(self.stats_label)

        parent_layout.addWidget(status_group)

    def setup_mock_data(self):
        """设置模拟数据（实际使用时会连接真实OBS）"""
        self.mock_plugins = {
            "麦克风": {
                "Graillon音调": {
                    "plugin_id": "Auburn Sounds Graillon 3-64",
                    "filter_type": "vst_filter",
                    "plugin_path": "C:/Program Files/VstPlugins/Auburn Sounds/Graillon 3-64.dll",
                    "parameters": {
                        "pitch": {"value": 0.0, "type": "float", "range": [-12.0, 12.0], "description": "音调偏移（半音）"},
                        "formant": {"value": 100.0, "type": "float", "range": [50.0, 150.0], "description": "共振峰调节"},
                        "mix": {"value": 100.0, "type": "float", "range": [0.0, 100.0], "description": "干湿混合比例"},
                        "bypass": {"value": False, "type": "bool", "range": [False, True], "description": "旁路开关"},
                        "param_0": {"value": 0.0, "type": "float", "range": [0.0, 1.0], "description": "通用参数0"},
                        "param_1": {"value": 0.5, "type": "float", "range": [0.0, 1.0], "description": "通用参数1"}
                    }
                },
                "TSE808失真": {
                    "plugin_id": "TSE_808_2.0_x64",
                    "filter_type": "vst_filter",
                    "plugin_path": "C:/Program Files/VstPlugins/TSE/TSE_808_2.0_x64.dll",
                    "parameters": {
                        "drive": {"value": 30.0, "type": "float", "range": [0.0, 100.0], "description": "驱动强度"},
                        "tone": {"value": 50.0, "type": "float", "range": [0.0, 100.0], "description": "音色调节"},
                        "level": {"value": 80.0, "type": "float", "range": [0.0, 100.0], "description": "输出电平"},
                        "enabled": {"value": True, "type": "bool", "range": [False, True], "description": "启用状态"},
                        "Drive": {"value": 30.0, "type": "float", "range": [0.0, 100.0], "description": "驱动强度（大写）"},
                        "Tone": {"value": 50.0, "type": "float", "range": [0.0, 100.0], "description": "音色调节（大写）"}
                    }
                },
                "TAL混响": {
                    "plugin_id": "TAL-Reverb-4-64",
                    "filter_type": "vst_filter",
                    "plugin_path": "C:/Program Files/VstPlugins/TAL/TAL-Reverb-4-64.dll",
                    "parameters": {
                        "roomsize": {"value": 40.0, "type": "float", "range": [0.0, 100.0], "description": "房间大小"},
                        "damping": {"value": 60.0, "type": "float", "range": [0.0, 100.0], "description": "阻尼系数"},
                        "mix": {"value": 25.0, "type": "float", "range": [0.0, 100.0], "description": "混响混合"},
                        "predelay": {"value": 0.0, "type": "float", "range": [0.0, 200.0], "description": "预延迟"},
                        "RoomSize": {"value": 40.0, "type": "float", "range": [0.0, 100.0], "description": "房间大小（大写）"},
                        "Damping": {"value": 60.0, "type": "float", "range": [0.0, 100.0], "description": "阻尼系数（大写）"}
                    }
                }
            }
        }

    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"

        self.log_text.append(log_message)

        if self.auto_scroll_cb.isChecked():
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.End)
            self.log_text.setTextCursor(cursor)

        # 同时更新状态栏
        self.status_label.setText(message)

    def toggle_obs_connection(self):
        """切换OBS连接状态"""
        if not self.is_connected:
            # 模拟连接过程
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 使用定时器模拟连接延迟
            QTimer.singleShot(2000, self.complete_connection)
        else:
            # 断开连接
            self.disconnect_obs()

    def complete_connection(self):
        """完成OBS连接"""
        self.is_connected = True
        self.connection_label.setText("✅ 已连接到OBS")
        self.connection_label.setStyleSheet("color: #00ff88; font-weight: bold;")
        self.connect_btn.setText("🔌 断开连接")
        self.connect_btn.setEnabled(True)

        # 启用相关按钮
        self.refresh_btn.setEnabled(True)
        self.detect_btn.setEnabled(True)
        self.auto_detect_btn.setEnabled(True)

        self.progress_bar.setVisible(False)
        self.log("✅ 成功连接到OBS WebSocket")

        # 自动刷新插件列表
        self.refresh_plugins()

    def disconnect_obs(self):
        """断开OBS连接"""
        self.is_connected = False
        self.connection_label.setText("❌ 未连接到OBS")
        self.connection_label.setStyleSheet("color: #ff4444; font-weight: bold;")
        self.connect_btn.setText("🔗 连接OBS")

        # 禁用相关按钮
        self.refresh_btn.setEnabled(False)
        self.detect_btn.setEnabled(False)
        self.auto_detect_btn.setEnabled(False)
        self.analyze_btn.setEnabled(False)
        self.refresh_params_btn.setEnabled(False)
        self.export_btn.setEnabled(False)

        # 停止监控
        if self.monitor_timer.isActive():
            self.monitor_timer.stop()
            self.monitor_enabled_cb.setChecked(False)

        self.log("🔌 已断开OBS连接")

    def on_source_changed(self):
        """音频源改变时的处理"""
        if self.is_connected:
            self.log(f"📋 切换到音频源: {self.source_combo.currentText()}")
            self.refresh_plugins()

    def refresh_plugins(self):
        """刷新插件列表"""
        if not self.is_connected:
            return

        self.log("🔄 正在刷新VST插件列表...")
        source_name = self.source_combo.currentText()

        # 清空当前列表
        self.plugin_table.setRowCount(0)

        # 获取模拟数据
        if source_name in self.mock_plugins:
            plugins = self.mock_plugins[source_name]
            self.plugin_table.setRowCount(len(plugins))

            for row, (filter_name, plugin_data) in enumerate(plugins.items()):
                # 插件名称
                name_item = QTableWidgetItem(filter_name)
                name_item.setData(Qt.UserRole, plugin_data)  # 存储完整数据
                self.plugin_table.setItem(row, 0, name_item)

                # 插件类型
                plugin_id = plugin_data.get("plugin_id", "Unknown")
                type_item = QTableWidgetItem(plugin_id)
                self.plugin_table.setItem(row, 1, type_item)

                # 状态
                param_count = len(plugin_data.get("parameters", {}))
                status_item = QTableWidgetItem(f"✅ {param_count}个参数")
                status_item.setForeground(QColor("#00ff88"))
                self.plugin_table.setItem(row, 2, status_item)

            self.log(f"✅ 发现 {len(plugins)} 个VST插件")
            self.update_stats()
        else:
            self.log(f"ℹ️ 音频源 '{source_name}' 没有VST插件")

    def update_stats(self):
        """更新统计信息"""
        plugin_count = self.plugin_table.rowCount()
        total_params = 0

        for row in range(plugin_count):
            item = self.plugin_table.item(row, 0)
            if item:
                plugin_data = item.data(Qt.UserRole)
                if plugin_data:
                    total_params += len(plugin_data.get("parameters", {}))

        self.stats_label.setText(f"插件: {plugin_count} | 参数: {total_params}")

    def on_plugin_selected(self):
        """插件选择改变时的处理"""
        selected_items = self.plugin_table.selectedItems()
        if not selected_items:
            self.analyze_btn.setEnabled(False)
            self.refresh_params_btn.setEnabled(False)
            return

        # 获取选中的插件数据
        row = selected_items[0].row()
        name_item = self.plugin_table.item(row, 0)
        plugin_data = name_item.data(Qt.UserRole)

        if plugin_data:
            self.current_plugin_data = plugin_data
            self.analyze_btn.setEnabled(True)
            self.refresh_params_btn.setEnabled(True)

            # 更新插件信息显示
            self.update_plugin_info_display(name_item.text(), plugin_data)

    def update_plugin_info_display(self, plugin_name, plugin_data):
        """更新插件信息显示"""
        self.plugin_name_label.setText(plugin_name)
        self.plugin_id_label.setText(plugin_data.get("plugin_id", "Unknown"))
        self.filter_type_label.setText(plugin_data.get("filter_type", "Unknown"))

        params = plugin_data.get("parameters", {})
        self.param_count_label.setText(str(len(params)))
        self.plugin_path_label.setText(plugin_data.get("plugin_path", "Unknown"))

        # 更新详细信息
        details = f"插件名称: {plugin_name}\n"
        details += f"插件ID: {plugin_data.get('plugin_id', 'Unknown')}\n"
        details += f"滤镜类型: {plugin_data.get('filter_type', 'Unknown')}\n"
        details += f"插件路径: {plugin_data.get('plugin_path', 'Unknown')}\n"
        details += f"参数数量: {len(params)}\n\n"
        details += "参数概览:\n"
        details += "-" * 30 + "\n"

        for param_name, param_info in params.items():
            param_type = param_info.get("type", "unknown")
            param_value = param_info.get("value", "N/A")
            details += f"• {param_name}: {param_value} ({param_type})\n"

        self.plugin_details_text.setPlainText(details)
