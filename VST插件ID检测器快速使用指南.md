# 🎛️ VST插件ID检测器快速使用指南

## 🚀 快速开始

### 1. 启动程序
```bash
# 在E:/OBS目录下运行
E:\OBS\.venv\Scripts\python.exe test_vst_detector.py
```

### 2. 连接OBS
1. **确保OBS Studio正在运行**
2. **确保OBS WebSocket服务器已启用**：
   - 工具 → WebSocket服务器设置
   - 勾选"启用WebSocket服务器"
   - 端口：4455（默认）
   - 建议取消"启用身份验证"
3. **在程序中点击"连接OBS"按钮**
4. **等待显示"✅ 已连接"**

### 3. 检测你的VST插件
1. **选择音频源**：在下拉菜单中选择包含VST插件的音频源
2. **点击"🔍 检测VST"按钮**
3. **查看检测结果**：程序会显示找到的VST插件详细信息

## 🎯 针对你的三个VST插件

### Auburn Sounds Graillon 3-64 🎵
这个插件用于音调变化，检测后你会看到类似：
```
🎛️ VST插件详细信息:
滤镜名称: Graillon音调
插件类型: vst_filter
启用状态: ✅ 启用

📊 插件参数:
  • pitch: 0.0 (float) - 音调偏移参数
  • formant: 100.0 (float) - 共振峰调节
  • mix: 100.0 (float) - 干湿混合比例
```

### TSE_808_2.0_x64 🔥
这个插件用于失真效果，检测后你会看到：
```
🎛️ VST插件详细信息:
滤镜名称: TSE808失真
插件类型: vst_filter
启用状态: ✅ 启用

📊 插件参数:
  • drive: 30.0 (float) - 驱动强度
  • tone: 50.0 (float) - 音色调节
  • level: 80.0 (float) - 输出电平
```

### TAL-Reverb-4-64 🌊
这个插件用于混响效果，检测后你会看到：
```
🎛️ VST插件详细信息:
滤镜名称: TAL混响
插件类型: vst_filter
启用状态: ✅ 启用

📊 插件参数:
  • roomsize: 40.0 (float) - 房间大小
  • damping: 60.0 (float) - 阻尼系数
  • mix: 25.0 (float) - 混响混合
```

## 💾 导出检测结果

1. **检测完成后，点击"💾 导出"按钮**
2. **选择保存位置**
3. **文件会保存为JSON格式**，包含所有插件的详细信息

### 导出文件示例
```json
{
  "timestamp": "2025-07-28 16:30:45",
  "detection_results": {
    "麦克风": {
      "Graillon音调": {
        "filterName": "Graillon音调",
        "filterKind": "vst_filter",
        "filterEnabled": true,
        "filterSettings": {
          "pitch": 0.0,
          "formant": 100.0,
          "mix": 100.0
        }
      },
      "TSE808失真": {
        "filterName": "TSE808失真", 
        "filterKind": "vst_filter",
        "filterEnabled": true,
        "filterSettings": {
          "drive": 30.0,
          "tone": 50.0,
          "level": 80.0
        }
      },
      "TAL混响": {
        "filterName": "TAL混响",
        "filterKind": "vst_filter", 
        "filterEnabled": true,
        "filterSettings": {
          "roomsize": 40.0,
          "damping": 60.0,
          "mix": 25.0
        }
      }
    }
  }
}
```

## 🔧 如何在主程序中使用这些信息

获取到插件信息后，你可以在主程序中这样使用：

### 1. 音调控制（Graillon）
```python
# 使用检测到的参数名称
source_name = "麦克风"
filter_name = "Graillon音调"
param_name = "pitch"  # 从检测结果中获得

# 设置音调值
self.set_vst_filter_property(source_name, filter_name, param_name, -3.0)
```

### 2. 失真控制（TSE808）
```python
# 使用检测到的参数名称
source_name = "麦克风"
filter_name = "TSE808失真"

# 设置失真参数
self.set_vst_filter_property(source_name, filter_name, "drive", 50.0)
self.set_vst_filter_property(source_name, filter_name, "tone", 60.0)
```

### 3. 混响控制（TAL）
```python
# 使用检测到的参数名称
source_name = "麦克风"
filter_name = "TAL混响"

# 设置混响参数
self.set_vst_filter_property(source_name, filter_name, "roomsize", 60.0)
self.set_vst_filter_property(source_name, filter_name, "mix", 30.0)
```

## ⚠️ 常见问题

### 连接失败
- 确保OBS正在运行
- 检查WebSocket端口（默认4455）
- 确认WebSocket服务器已启用

### 找不到音频源
- 确保在OBS中添加了音频源（麦克风、桌面音频等）
- 点击"🔄 刷新源"按钮重新扫描

### 检测不到VST插件
- 确认VST插件已正确添加到音频源的滤镜中
- 检查滤镜是否启用
- 确认滤镜名称正确

## 🎯 使用建议

1. **先在OBS中测试VST插件**：确保插件工作正常
2. **记录滤镜名称**：确保与程序中使用的名称一致
3. **测试参数范围**：了解每个参数的有效取值范围
4. **保存检测结果**：导出JSON文件作为参考

## 📞 技术支持

如果遇到问题，请：
1. 查看程序窗口中的日志信息
2. 确认OBS和VST插件设置
3. 检查网络连接和端口设置
