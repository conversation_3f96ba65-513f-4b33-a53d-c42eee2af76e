import websocket  
import json  
import time  


# ======== OBS WebSocket配置（无需密码版） ========  
OBS_WS_URL = "ws://localhost:4455"  # 端口默认4455，若OBS中修改过需同步  
OBS_PASSWORD = ""  # 无密码，直接设为空字符串  
# ==============================================  


def on_open(ws):  
    print("已连接OBS WebSocket（无需密码），开始监听参数变化...")  
    # 【关键】无需密码时，跳过认证步骤，直接订阅事件  
    subscribe_request = {  
        "request-type": "Subscribe",  
        "message-id": "sub1",  
        "events": ["FilterParamChanged"]  # 订阅滤镜参数变化事件  
    }  
    ws.send(json.dumps(subscribe_request))  


def on_message(ws, message):  
    message_data = json.loads(message)  
    # 仅处理"FilterParamChanged"事件（参数变化时触发）  
    if message_data.get("update-type") == "FilterParamChanged":  
        event_data = message_data  
        print("\n===== 检测到参数变化 =====")  
        print(f"音频源名称：{event_data['inputName']}")  
        print(f"滤镜名称：{event_data['filterName']}")  
        print(f"参数ID：{event_data['param']}")  # 调节按钮ID（数字）  
        print(f"当前值：{event_data['value']}")  


def on_error(ws, error):  
    print("错误：", error)  


def on_close(ws):  
    print("连接关闭（若频繁关闭，检查OBS端口是否正确或obs-websocket版本是否为4.x）")  


if __name__ == "__main__":  
    # 启动WebSocket连接（无需密码，直接连接）  
    ws = websocket.WebSocketApp(  
        OBS_WS_URL,  
        on_open=on_open,  
        on_message=on_message,  
        on_error=on_error,  
        on_close=on_close  
    )  
    ws.run_forever()  # 保持连接，监听事件  