#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST参数验证工具
用于验证哪些参数名称在你的VST插件中真正有效
"""

import obsws_python as obs
import time
import json
from datetime import datetime

class VSTParameterValidator:
    def __init__(self, host="localhost", port=4455, password=""):
        """初始化OBS WebSocket连接"""
        try:
            self.client = obs.ReqClient(host=host, port=port, password=password)
            print("✅ 成功连接到OBS WebSocket")
        except Exception as e:
            print(f"❌ 连接OBS失败: {e}")
            print("请确保:")
            print("1. OBS已启动")
            print("2. WebSocket服务器已启用 (工具 → WebSocket服务器设置)")
            print("3. 端口和密码设置正确")
            raise
    
    def get_current_filter_settings(self, source_name, filter_name):
        """获取滤镜当前设置"""
        try:
            response = self.client.get_source_filter(
                source_name=source_name,
                filter_name=filter_name
            )
            return response.filter_settings
        except Exception as e:
            print(f"❌ 获取滤镜设置失败: {e}")
            return None
    
    def test_parameter_names(self, source_name, filter_name, param_category, test_value=0.5):
        """测试一类参数的所有可能名称"""
        
        # 参数名称数据库
        PARAM_DATABASE = {
            "pitch": [
                "pitch", "Pitch", "PITCH",
                "tune", "Tune", "TUNE", 
                "correction", "Correction", "CORRECTION",
                "shift", "Shift", "SHIFT",
                "transpose", "Transpose", "TRANSPOSE",
                "semitones", "Semitones", "SEMITONES",
                "cents", "Cents", "CENTS",
                "detune", "Detune", "DETUNE",
                "formant", "Formant", "FORMANT",
                "key", "Key", "KEY"
            ],
            "distortion": [
                "drive", "Drive", "DRIVE",
                "gain", "Gain", "GAIN",
                "overdrive", "Overdrive", "OVERDRIVE",
                "distortion", "Distortion", "DISTORTION",
                "saturation", "Saturation", "SATURATION",
                "intensity", "Intensity", "INTENSITY",
                "amount", "Amount", "AMOUNT",
                "level", "Level", "LEVEL",
                "input", "Input", "INPUT",
                "output", "Output", "OUTPUT"
            ],
            "tone": [
                "tone", "Tone", "TONE",
                "eq", "EQ", "Eq",
                "treble", "Treble", "TREBLE",
                "bass", "Bass", "BASS",
                "mid", "Mid", "MID",
                "high", "High", "HIGH",
                "low", "Low", "LOW",
                "color", "Color", "COLOR",
                "timbre", "Timbre", "TIMBRE"
            ],
            "reverb_size": [
                "roomsize", "RoomSize", "ROOMSIZE",
                "size", "Size", "SIZE",
                "room", "Room", "ROOM",
                "space", "Space", "SPACE",
                "hall", "Hall", "HALL",
                "decay", "Decay", "DECAY",
                "length", "Length", "LENGTH"
            ],
            "reverb_mix": [
                "mix", "Mix", "MIX",
                "wet", "Wet", "WET",
                "dry", "Dry", "DRY",
                "blend", "Blend", "BLEND",
                "balance", "Balance", "BALANCE",
                "send", "Send", "SEND",
                "return", "Return", "RETURN"
            ]
        }
        
        param_names = PARAM_DATABASE.get(param_category, [])
        if not param_names:
            print(f"❌ 未知参数类别: {param_category}")
            return []
        
        print(f"\n🧪 测试 {param_category} 参数 (共 {len(param_names)} 种可能名称)")
        print("=" * 60)
        
        # 获取原始设置作为基准
        original_settings = self.get_current_filter_settings(source_name, filter_name)
        if not original_settings:
            return []
        
        valid_params = []
        
        for i, param_name in enumerate(param_names, 1):
            print(f"[{i:2d}/{len(param_names)}] 测试参数: {param_name}", end=" ")
            
            try:
                # 尝试设置参数
                self.client.set_source_filter_settings(
                    source_name=source_name,
                    filter_name=filter_name,
                    filter_settings={param_name: test_value}
                )
                
                # 等待一小段时间让设置生效
                time.sleep(0.1)
                
                # 检查设置是否真的改变了
                new_settings = self.get_current_filter_settings(source_name, filter_name)
                
                if new_settings and param_name in new_settings:
                    current_value = new_settings[param_name]
                    original_value = original_settings.get(param_name, "不存在")
                    
                    if current_value != original_value:
                        print(f"✅ 有效! ({original_value} → {current_value})")
                        valid_params.append({
                            "name": param_name,
                            "original_value": original_value,
                            "test_value": current_value,
                            "type": type(current_value).__name__
                        })
                    else:
                        print(f"⚠️ 参数存在但未改变")
                else:
                    print("❌ 无效")
                    
            except Exception as e:
                print(f"❌ 错误: {str(e)[:30]}...")
        
        # 恢复原始设置
        try:
            self.client.set_source_filter_settings(
                source_name=source_name,
                filter_name=filter_name,
                filter_settings=original_settings
            )
            print(f"\n🔄 已恢复原始设置")
        except:
            print(f"\n⚠️ 恢复原始设置失败")
        
        return valid_params
    
    def full_validation(self, source_name, filter_name):
        """完整验证所有参数类别"""
        print(f"🎛️ 开始完整验证 VST 插件: {filter_name}")
        print(f"📍 音频源: {source_name}")
        print("=" * 80)
        
        # 首先显示当前所有参数
        current_settings = self.get_current_filter_settings(source_name, filter_name)
        if current_settings:
            print(f"\n📋 当前插件参数 (共 {len(current_settings)} 个):")
            print("-" * 50)
            for key, value in current_settings.items():
                print(f"  {key}: {value} ({type(value).__name__})")
        
        # 测试各类参数
        all_valid_params = {}
        test_categories = ["pitch", "distortion", "tone", "reverb_size", "reverb_mix"]
        
        for category in test_categories:
            valid_params = self.test_parameter_names(source_name, filter_name, category)
            if valid_params:
                all_valid_params[category] = valid_params
        
        # 生成报告
        self.generate_report(source_name, filter_name, all_valid_params, current_settings)
        
        return all_valid_params
    
    def generate_report(self, source_name, filter_name, valid_params, current_settings):
        """生成验证报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"VST参数验证报告_{filter_name}_{timestamp}.txt"
        
        report_content = f"""
🎛️ VST插件参数验证报告
=====================================
插件名称: {filter_name}
音频源: {source_name}
验证时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 验证结果总览:
"""
        
        total_valid = sum(len(params) for params in valid_params.values())
        report_content += f"✅ 发现有效参数: {total_valid} 个\n"
        report_content += f"📋 插件总参数: {len(current_settings)} 个\n\n"
        
        # 详细结果
        for category, params in valid_params.items():
            report_content += f"\n🎯 {category.upper()} 参数 ({len(params)} 个有效):\n"
            report_content += "-" * 40 + "\n"
            
            for param in params:
                report_content += f"  ✅ {param['name']}: {param['type']}\n"
                report_content += f"     原始值: {param['original_value']}\n"
                report_content += f"     测试值: {param['test_value']}\n\n"
        
        # 生成控制代码
        report_content += "\n💻 推荐的控制代码:\n"
        report_content += "=" * 40 + "\n"
        
        for category, params in valid_params.items():
            if params:
                best_param = params[0]['name']  # 使用第一个有效参数
                report_content += f"""
# {category} 控制
def control_{category}(client, source_name, filter_name, value):
    client.set_source_filter_settings(
        source_name=source_name,
        filter_name=filter_name,
        filter_settings={{"{best_param}": value}}
    )
"""
        
        # 保存报告
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"\n📄 验证报告已保存: {report_file}")
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")
        
        # 显示总结
        print(f"\n🎉 验证完成!")
        print(f"✅ 发现 {total_valid} 个有效参数")
        print(f"📄 详细报告: {report_file}")

def main():
    """主函数"""
    print("🎛️ VST参数验证工具")
    print("=" * 50)
    
    # 配置信息 - 请根据你的实际情况修改
    SOURCE_NAME = "麦克风"  # 你的音频源名称
    FILTER_NAME = "Graillon"  # 你的VST滤镜名称
    WEBSOCKET_PASSWORD = ""  # 你的WebSocket密码
    
    try:
        validator = VSTParameterValidator(password=WEBSOCKET_PASSWORD)
        validator.full_validation(SOURCE_NAME, FILTER_NAME)
        
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        print("\n🔧 请检查:")
        print("1. OBS是否正在运行")
        print("2. WebSocket服务器是否已启用")
        print("3. 音频源和滤镜名称是否正确")
        print("4. 是否安装了 obsws-python: pip install obsws-python")

if __name__ == "__main__":
    main()
