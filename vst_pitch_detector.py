#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎵 VST音调参数检测器
专门用于找到控制音调的参数
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class VSTParameterDetector:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎵 VST音调参数检测器")
        self.root.geometry("1000x600")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 监控状态
        self.monitoring = False
        self.baseline_params = {}
        self.current_params = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST信息区域
        info_frame = ttk.LabelFrame(main_frame, text="🎛️ VST插件信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_row = ttk.Frame(info_frame)
        info_row.pack(fill=tk.X)
        
        ttk.Label(info_row, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(info_row, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(info_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(info_row, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # 检测按钮区域
        detect_frame = ttk.LabelFrame(main_frame, text="🔍 音调参数检测", padding="10")
        detect_frame.pack(fill=tk.X, pady=(0, 10))
        
        btn_row1 = ttk.Frame(detect_frame)
        btn_row1.pack(fill=tk.X, pady=(0, 5))
        
        self.baseline_btn = ttk.Button(btn_row1, text="📊 记录基准状态", 
                                      command=self.record_baseline, state="disabled")
        self.baseline_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.detect_btn = ttk.Button(btn_row1, text="🎵 检测音调变化", 
                                    command=self.detect_pitch_changes, state="disabled")
        self.detect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.monitor_btn = ttk.Button(btn_row1, text="📡 开始实时监控", 
                                     command=self.toggle_monitoring, state="disabled")
        self.monitor_btn.pack(side=tk.LEFT)
        
        btn_row2 = ttk.Frame(detect_frame)
        btn_row2.pack(fill=tk.X)
        
        self.show_all_btn = ttk.Button(btn_row2, text="📋 显示所有参数", 
                                      command=self.show_all_params, state="disabled")
        self.show_all_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_common_btn = ttk.Button(btn_row2, text="🧪 测试常见音调参数", 
                                         command=self.test_common_pitch_params, state="disabled")
        self.test_common_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 检测结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🎵 VST音调参数检测器启动")
        self.log("这个工具专门用于找到控制音调的参数")
        self.log("使用步骤:")
        self.log("1. 连接OBS")
        self.log("2. 设置正确的音频源和VST滤镜名称")
        self.log("3. 点击'记录基准状态'")
        self.log("4. 在OBS中调节音调参数")
        self.log("5. 点击'检测音调变化'")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有按钮
        buttons = [self.baseline_btn, self.detect_btn, self.monitor_btn, 
                  self.show_all_btn, self.test_common_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("💡 现在可以开始检测音调参数了")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.baseline_btn, self.detect_btn, self.monitor_btn, 
                  self.show_all_btn, self.test_common_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
    
    def get_current_params(self):
        """获取当前参数"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})
            return {k: v for k, v in settings.items() if k != 'plugin_path'}
        return {}
    
    def record_baseline(self):
        """记录基准状态"""
        self.log("📊 记录基准参数状态...")
        self.baseline_params = self.get_current_params()

        if self.baseline_params:
            self.log(f"✅ 已记录 {len(self.baseline_params)} 个基准参数:")
            for key, value in self.baseline_params.items():
                try:
                    num_val = float(value)
                    self.log(f"  📊 {key}: {num_val}")
                except:
                    self.log(f"  📝 {key}: {value}")
            self.log("💡 现在请在OBS中调节音调参数，然后点击'检测音调变化'")
        else:
            self.log("❌ 获取基准参数失败")

    def detect_pitch_changes(self):
        """检测音调变化"""
        if not self.baseline_params:
            self.log("⚠️ 请先记录基准状态")
            return

        self.log("🎵 检测音调参数变化...")
        self.current_params = self.get_current_params()

        if not self.current_params:
            self.log("❌ 获取当前参数失败")
            return

        changes_found = []

        # 比较所有参数
        for key in self.baseline_params:
            if key in self.current_params:
                old_val = self.baseline_params[key]
                new_val = self.current_params[key]

                try:
                    old_num = float(old_val)
                    new_num = float(new_val)
                    # 使用更宽松的检测阈值
                    if abs(new_num - old_num) > 0.0001:  # 非常敏感的检测
                        changes_found.append({
                            'param': key,
                            'old': old_num,
                            'new': new_num,
                            'diff': new_num - old_num,
                            'type': 'numeric'
                        })
                except:
                    if str(old_val) != str(new_val):
                        changes_found.append({
                            'param': key,
                            'old': old_val,
                            'new': new_val,
                            'type': 'string'
                        })

        # 显示结果
        if changes_found:
            self.log(f"🎯 发现 {len(changes_found)} 个参数变化:")
            self.log("=" * 50)

            for change in changes_found:
                if change['type'] == 'numeric':
                    self.log(f"📊 {change['param']}:")
                    self.log(f"   原值: {change['old']:.6f}")
                    self.log(f"   新值: {change['new']:.6f}")
                    self.log(f"   变化: {change['diff']:+.6f}")

                    # 分析可能的音调参数
                    if self.is_likely_pitch_param(change['param'], change['diff']):
                        self.log(f"   🎵 可能是音调参数！")
                else:
                    self.log(f"📝 {change['param']}: {change['old']} → {change['new']}")

            self.log("=" * 50)
        else:
            self.log("ℹ️ 未检测到参数变化")
            self.log("💡 请确保:")
            self.log("  1. 在OBS中确实调节了VST参数")
            self.log("  2. 音频源和滤镜名称正确")
            self.log("  3. VST插件正常工作")

    def is_likely_pitch_param(self, param_name, diff):
        """判断是否可能是音调参数"""
        param_lower = param_name.lower()

        # 检查参数名称
        pitch_keywords = ['pitch', 'tune', 'semitone', 'transpose', 'coarse', 'fine', 'freq']
        if any(keyword in param_lower for keyword in pitch_keywords):
            return True

        # 检查数值变化范围（音调参数通常在-24到+24半音之间）
        if abs(diff) > 0.1 and abs(diff) < 50:
            return True

        return False

    def toggle_monitoring(self):
        """切换实时监控"""
        if not self.monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()

    def start_monitoring(self):
        """开始实时监控"""
        self.monitoring = True
        self.monitor_btn.config(text="📡 停止监控")
        self.log("📡 开始实时监控所有参数变化...")

        # 记录初始状态
        self.baseline_params = self.get_current_params()

        def monitor_loop():
            while self.monitoring and self.is_connected:
                try:
                    current = self.get_current_params()

                    for key, value in current.items():
                        if key in self.baseline_params:
                            old_val = self.baseline_params[key]
                            try:
                                old_num = float(old_val)
                                new_num = float(value)
                                if abs(new_num - old_num) > 0.0001:
                                    diff = new_num - old_num
                                    self.root.after(0, self.log,
                                        f"📊 {key}: {old_num:.6f} → {new_num:.6f} ({diff:+.6f})")

                                    if self.is_likely_pitch_param(key, diff):
                                        self.root.after(0, self.log, f"   🎵 可能是音调参数！")

                                    self.baseline_params[key] = value
                            except:
                                if str(old_val) != str(value):
                                    self.root.after(0, self.log,
                                        f"📝 {key}: {old_val} → {value}")
                                    self.baseline_params[key] = value

                    time.sleep(0.2)  # 每0.2秒检查一次，非常快速

                except Exception as e:
                    self.root.after(0, self.log, f"❌ 监控错误: {e}")
                    break

        threading.Thread(target=monitor_loop, daemon=True).start()

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.monitor_btn.config(text="📡 开始实时监控")
        self.log("📡 停止实时监控")

    def show_all_params(self):
        """显示所有参数"""
        self.log("📋 显示所有当前参数:")
        params = self.get_current_params()

        if params:
            self.log("=" * 50)
            numeric_params = []
            string_params = []

            for key, value in params.items():
                try:
                    num_val = float(value)
                    numeric_params.append((key, num_val))
                except:
                    string_params.append((key, value))

            # 显示数值参数
            if numeric_params:
                self.log("📊 数值参数:")
                for key, value in sorted(numeric_params):
                    param_lower = key.lower()
                    if any(keyword in param_lower for keyword in ['pitch', 'tune', 'semitone', 'transpose', 'coarse', 'fine', 'freq']):
                        self.log(f"  🎵 {key}: {value:.6f} ← 可能是音调参数")
                    else:
                        self.log(f"  📊 {key}: {value:.6f}")

            # 显示字符串参数
            if string_params:
                self.log("\n📝 非数值参数:")
                for key, value in sorted(string_params):
                    self.log(f"  📝 {key}: {value}")

            self.log("=" * 50)
            self.log(f"✅ 共 {len(params)} 个参数 ({len(numeric_params)} 数值, {len(string_params)} 非数值)")
        else:
            self.log("❌ 获取参数失败")

    def test_common_pitch_params(self):
        """测试常见的音调参数名称"""
        self.log("🧪 测试常见音调参数名称...")

        # 常见的音调参数名称
        common_pitch_params = [
            'pitch', 'Pitch', 'PITCH',
            'tune', 'Tune', 'TUNE',
            'semitones', 'Semitones', 'SEMITONES',
            'transpose', 'Transpose', 'TRANSPOSE',
            'coarse', 'Coarse', 'COARSE',
            'fine', 'Fine', 'FINE',
            'frequency', 'Frequency', 'FREQUENCY',
            'freq', 'Freq', 'FREQ',
            'cents', 'Cents', 'CENTS',
            'detune', 'Detune', 'DETUNE',
            'shift', 'Shift', 'SHIFT',
            'param_0', 'param_1', 'param_2', 'param_3',
            'parameter_0', 'parameter_1', 'parameter_2', 'parameter_3',
            '0', '1', '2', '3', '4', '5'
        ]

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        found_params = []

        for param_name in common_pitch_params:
            self.log(f"🔍 测试参数: {param_name}")

            # 尝试设置一个测试值
            test_value = 2.0  # 2半音

            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: test_value
                }
            }, timeout=2)

            if response and response.get('requestStatus', {}).get('result'):
                # 验证设置
                time.sleep(0.3)
                verify_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                }, timeout=2)

                if verify_response and verify_response.get('requestStatus', {}).get('result'):
                    settings = verify_response.get('responseData', {}).get('filterSettings', {})
                    if param_name in settings:
                        actual_value = settings[param_name]
                        try:
                            if abs(float(actual_value) - test_value) < 0.01:
                                self.log(f"  ✅ 找到有效参数: {param_name} = {actual_value}")
                                found_params.append(param_name)
                            else:
                                self.log(f"  ⚠️ 参数存在但值不匹配: {param_name} = {actual_value}")
                        except:
                            self.log(f"  📝 参数存在但非数值: {param_name} = {actual_value}")
                    else:
                        self.log(f"  ❌ 参数不存在: {param_name}")
                else:
                    self.log(f"  ❌ 验证失败: {param_name}")
            else:
                self.log(f"  ❌ 设置失败: {param_name}")

        self.log("=" * 50)
        if found_params:
            self.log(f"🎯 找到 {len(found_params)} 个可能的音调参数:")
            for param in found_params:
                self.log(f"  🎵 {param}")
            self.log("💡 请在OBS中播放音频，手动调节这些参数来验证效果")
        else:
            self.log("❌ 未找到有效的音调参数")
            self.log("💡 建议:")
            self.log("  1. 检查音频源和滤镜名称是否正确")
            self.log("  2. 确认VST插件已正确加载")
            self.log("  3. 尝试使用'显示所有参数'查看实际参数名称")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTParameterDetector()
    app.run()
