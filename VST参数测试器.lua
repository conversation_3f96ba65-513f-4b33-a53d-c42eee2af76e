obs = obslua

-- 测试配置
local test_settings = {
    source_name = "",
    filter_name = "",
    test_active = false
}

-- 常见的VST参数名称列表
local common_vst_params = {
    -- Graillon可能的参数名
    "pitch", "Pitch", "PITCH",
    "formant", "Formant", "FORMANT", 
    "mix", "Mix", "MIX",
    "wet", "Wet", "WET",
    "dry", "Dry", "DRY",
    
    -- TAL Reverb可能的参数名
    "roomsize", "RoomSize", "ROOMSIZE", "room_size",
    "damping", "Damping", "DAMPING",
    "predelay", "PreDelay", "PREDELAY", "pre_delay",
    "lowcut", "LowCut", "LOWCUT", "low_cut",
    "highcut", "HighCut", "HIGHCUT", "high_cut",
    
    -- TSE808可能的参数名
    "drive", "Drive", "DRIVE",
    "tone", "Tone", "TONE", 
    "level", "Level", "LEVEL",
    "output", "Output", "OUTPUT",
    "gain", "Gain", "GAIN",
    
    -- 通用数字参数
    "param_0", "param_1", "param_2", "param_3", "param_4",
    "parameter_0", "parameter_1", "parameter_2", "parameter_3",
    "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
}

-- 日志函数
local function log(message, level)
    obs.script_log(level or obs.LOG_INFO, "[VST测试器] " .. tostring(message))
end

-- 获取所有音频源
local function get_audio_sources()
    local sources = {}
    local all_sources = obs.obs_enum_sources()
    if all_sources then
        for _, src in ipairs(all_sources) do
            local name = obs.obs_source_get_name(src)
            table.insert(sources, name)
            obs.obs_source_release(src)
        end
    end
    return sources
end

-- 获取指定音频源的所有滤镜
local function get_source_filters(source_name)
    local filters = {}
    local source = obs.obs_get_source_by_name(source_name)
    if not source then return filters end
    
    -- 这里需要枚举滤镜，但OBS Lua API可能没有直接的方法
    -- 我们提供一个手动输入的方式
    obs.obs_source_release(source)
    return filters
end

-- 测试单个参数
local function test_parameter(source_name, filter_name, param_name, test_value)
    local source = obs.obs_get_source_by_name(source_name)
    if not source then
        log("音频源不存在: " .. source_name, obs.LOG_WARNING)
        return false
    end
    
    local filter = obs.obs_source_get_filter_by_name(source, filter_name)
    if not filter then
        log("滤镜不存在: " .. filter_name, obs.LOG_WARNING)
        obs.obs_source_release(source)
        return false
    end
    
    local filter_settings = obs.obs_source_get_settings(filter)
    
    -- 尝试设置参数
    obs.obs_data_set_double(filter_settings, param_name, test_value)
    obs.obs_source_update(filter, filter_settings)
    
    log(string.format("测试参数: %s = %.2f", param_name, test_value))
    
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    return true
end

-- 批量测试所有可能的参数名
local function test_all_parameters()
    if not test_settings.source_name or test_settings.source_name == "" then
        log("请先选择音频源", obs.LOG_WARNING)
        return
    end
    
    if not test_settings.filter_name or test_settings.filter_name == "" then
        log("请先输入滤镜名称", obs.LOG_WARNING)
        return
    end
    
    log("开始批量测试VST参数...")
    log("音频源: " .. test_settings.source_name)
    log("滤镜名称: " .. test_settings.filter_name)
    log("=" * 50)
    
    local test_values = {0.0, 0.5, 1.0, 50.0, 100.0}
    local successful_params = {}
    
    for _, param_name in ipairs(common_vst_params) do
        for _, test_value in ipairs(test_values) do
            if test_parameter(test_settings.source_name, test_settings.filter_name, param_name, test_value) then
                table.insert(successful_params, param_name)
                break -- 如果成功设置了一个值，就认为这个参数名有效
            end
        end
    end
    
    log("=" * 50)
    log("测试完成！")
    
    if #successful_params > 0 then
        log("发现以下可能有效的参数名:")
        for _, param in ipairs(successful_params) do
            log("  ✅ " .. param)
        end
    else
        log("❌ 未发现有效的参数名")
        log("建议检查:")
        log("  1. 滤镜名称是否正确")
        log("  2. VST插件是否正确加载")
        log("  3. 尝试手动输入其他参数名")
    end
end

-- 脚本属性界面
function script_properties()
    local props = obs.obs_properties_create()
    
    -- 音频源选择
    local source_list = obs.obs_properties_add_list(props, "source_name", "🎤 选择音频源", 
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)
    
    for _, name in ipairs(get_audio_sources()) do
        obs.obs_property_list_add_string(source_list, name, name)
    end
    
    -- 滤镜名称输入
    obs.obs_properties_add_text(props, "filter_name", "🎛️ VST滤镜名称", obs.OBS_TEXT_DEFAULT)
    
    -- 测试按钮
    obs.obs_properties_add_button(props, "test_button", "🧪 开始参数测试", function()
        test_all_parameters()
        return true
    end)
    
    -- 手动测试区域
    obs.obs_properties_add_text(props, "manual_param", "🔧 手动测试参数名", obs.OBS_TEXT_DEFAULT)
    obs.obs_properties_add_float(props, "manual_value", "🔧 测试值", -100.0, 100.0, 0.1)
    obs.obs_properties_add_button(props, "manual_test_button", "🔧 手动测试", function()
        local param_name = obs.obs_data_get_string(obs.obs_frontend_get_current_scene_collection(), "manual_param")
        local test_value = obs.obs_data_get_double(obs.obs_frontend_get_current_scene_collection(), "manual_value")
        if param_name and param_name ~= "" then
            test_parameter(test_settings.source_name, test_settings.filter_name, param_name, test_value)
        else
            log("请输入参数名", obs.LOG_WARNING)
        end
        return true
    end)
    
    return props
end

-- 更新设置
function script_update(new_settings)
    test_settings.source_name = obs.obs_data_get_string(new_settings, "source_name")
    test_settings.filter_name = obs.obs_data_get_string(new_settings, "filter_name")
    
    log("设置已更新: 源=" .. test_settings.source_name .. ", 滤镜=" .. test_settings.filter_name)
end

-- 脚本描述
function script_description()
    return [[🧪 VST参数测试器
====================
用于测试和发现VST插件的参数名称

🎯 功能特点：
- 自动测试常见的VST参数名称
- 支持手动测试特定参数
- 详细的测试日志输出
- 适用于Graillon、TAL-Reverb、TSE808等插件

📋 使用步骤：
1. 选择包含VST滤镜的音频源
2. 输入VST滤镜的确切名称
3. 点击"开始参数测试"按钮
4. 查看日志输出中的有效参数名

🔧 手动测试：
- 如果自动测试没有找到参数
- 可以手动输入参数名进行测试
- 建议测试值范围：0-100

⚠️ 注意事项：
- 滤镜名称必须与OBS中显示的完全一致
- 测试过程中可能会听到音频变化
- 建议在测试时降低音量]]
end
