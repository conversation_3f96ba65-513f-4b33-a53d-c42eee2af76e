#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎵 音调参数验证器
专门验证哪个参数真正控制音调
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class PitchParameterValidator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎵 音调参数验证器")
        self.root.geometry("800x600")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 候选音调参数（从您的日志中提取）
        self.pitch_candidates = [
            'pitch', 'pitch_shift', 'pitch_shift_amount', 'transpose', 
            'coarse', 'fine', 'tune', 'voice_pitch', 'pitch_bend',
            'pitch_correction', 'reference_frequency'
        ]
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST信息区域
        info_frame = ttk.LabelFrame(main_frame, text="🎛️ VST插件信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_row = ttk.Frame(info_frame)
        info_row.pack(fill=tk.X)
        
        ttk.Label(info_row, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(info_row, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(info_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(info_row, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # 测试区域
        test_frame = ttk.LabelFrame(main_frame, text="🧪 音调参数验证", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        btn_row = ttk.Frame(test_frame)
        btn_row.pack(fill=tk.X)
        
        self.validate_btn = ttk.Button(btn_row, text="🎵 验证音调参数", 
                                      command=self.validate_pitch_params, state="disabled")
        self.validate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_sequence_btn = ttk.Button(btn_row, text="🎭 音调序列测试", 
                                           command=self.test_pitch_sequence, state="disabled")
        self.test_sequence_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.reset_btn = ttk.Button(btn_row, text="🔄 重置所有参数", 
                                   command=self.reset_all_params, state="disabled")
        self.reset_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 验证结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🎵 音调参数验证器启动")
        self.log("这个工具将验证哪个参数真正控制音调")
        self.log("使用方法:")
        self.log("1. 连接OBS并播放音频")
        self.log("2. 点击'验证音调参数'")
        self.log("3. 仔细听每个参数测试时的音调变化")
        self.log("4. 记录哪个参数产生了明显的音调变化")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有按钮
        buttons = [self.validate_btn, self.test_sequence_btn, self.reset_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("💡 请确保OBS中有音频播放，然后开始验证")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.validate_btn, self.test_sequence_btn, self.reset_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
    
    def validate_pitch_params(self):
        """验证音调参数"""
        self.log("🎵 开始验证音调参数...")
        self.log("=" * 50)
        self.log("⚠️ 请仔细听音频变化！")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 获取当前参数值作为基准
        baseline_response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if not baseline_response or not baseline_response.get('requestStatus', {}).get('result'):
            self.log("❌ 获取基准参数失败")
            return

        baseline_settings = baseline_response.get('responseData', {}).get('filterSettings', {})

        # 测试每个候选参数
        for param_name in self.pitch_candidates:
            if param_name not in baseline_settings:
                self.log(f"⚠️ 跳过不存在的参数: {param_name}")
                continue

            original_value = baseline_settings[param_name]
            self.log(f"\n🧪 测试参数: {param_name}")
            self.log(f"   原始值: {original_value}")

            # 测试不同的音调值
            test_values = self.get_test_values_for_param(param_name, original_value)

            for test_value in test_values:
                self.log(f"   🎵 测试值: {test_value}")

                # 设置参数
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                }, timeout=3)

                if response and response.get('requestStatus', {}).get('result'):
                    self.log(f"      ✅ 设置成功")
                    self.log(f"      🎧 请听音调变化... (等待3秒)")
                    time.sleep(3)  # 给用户时间听音调变化
                else:
                    self.log(f"      ❌ 设置失败")

            # 恢复原始值
            self.log(f"   🔄 恢复原始值: {original_value}")
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: original_value
                }
            }, timeout=2)
            time.sleep(1)

        self.log("\n" + "=" * 50)
        self.log("🎵 音调参数验证完成！")
        self.log("💡 请记录哪个参数产生了明显的音调变化")

    def get_test_values_for_param(self, param_name, original_value):
        """根据参数名称和原始值生成测试值"""
        try:
            orig_val = float(original_value)
        except:
            orig_val = 0.0

        param_lower = param_name.lower()

        if 'pitch_shift_amount' in param_lower:
            # 音调偏移量，通常是半音数
            return [-12.0, -6.0, 0.0, 6.0, 12.0]
        elif 'pitch_shift' in param_lower or 'pitch' in param_lower:
            # 音调偏移，可能是半音或其他单位
            return [orig_val - 5, orig_val, orig_val + 5]
        elif 'transpose' in param_lower:
            # 移调，通常是半音数
            return [-12, -5, 0, 5, 12]
        elif 'coarse' in param_lower:
            # 粗调，通常是半音
            return [-12, -5, 0, 5, 12]
        elif 'fine' in param_lower:
            # 微调，通常是音分
            return [-50, -20, 0, 20, 50]
        elif 'tune' in param_lower:
            # 调音，可能是百分比或其他
            return [0, 25, 50, 75, 100]
        elif 'frequency' in param_lower:
            # 频率，通常是Hz
            return [220.0, 440.0, 880.0]
        else:
            # 默认测试值
            return [orig_val - 2, orig_val, orig_val + 2]

    def test_pitch_sequence(self):
        """测试音调序列（音阶）"""
        self.log("🎭 开始音调序列测试...")
        self.log("这将播放一个简单的音阶来验证音调控制")

        # 选择最可能的音调参数进行测试
        primary_params = ['pitch', 'pitch_shift', 'transpose', 'coarse']

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        for param_name in primary_params:
            self.log(f"\n🎵 使用参数 '{param_name}' 播放音阶:")

            # C大调音阶（半音偏移）
            scale_notes = [0, 2, 4, 5, 7, 9, 11, 12]  # C D E F G A B C
            note_names = ['C', 'D', 'E', 'F', 'G', 'A', 'B', 'C']

            for i, (semitone, note_name) in enumerate(zip(scale_notes, note_names)):
                self.log(f"   🎵 {note_name} ({semitone:+2d}半音)")

                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: semitone
                    }
                }, timeout=2)

                if response and response.get('requestStatus', {}).get('result'):
                    time.sleep(1)  # 每个音符持续1秒
                else:
                    self.log(f"      ❌ 设置失败")
                    break

            # 恢复到0
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: 0
                }
            }, timeout=2)

            self.log(f"   ✅ '{param_name}' 音阶测试完成")
            time.sleep(2)  # 参数间暂停

        self.log("\n🎭 音调序列测试完成！")
        self.log("💡 如果听到了音阶，说明对应的参数可以控制音调")

    def reset_all_params(self):
        """重置所有参数到默认值"""
        self.log("🔄 重置所有音调参数到默认值...")

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 默认值设置
        default_settings = {
            'pitch': 0.0,
            'pitch_shift': 0.0,
            'pitch_shift_amount': 0.0,
            'transpose': 0,
            'coarse': 0,
            'fine': 0,
            'tune': 50.0,
            'voice_pitch': 1.0,
            'pitch_bend': 0.5,
            'pitch_correction': 0.0,
            'reference_frequency': 440.0
        }

        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": default_settings
        }, timeout=5)

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 所有音调参数已重置")
        else:
            self.log("❌ 重置失败")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = PitchParameterValidator()
    app.run()
