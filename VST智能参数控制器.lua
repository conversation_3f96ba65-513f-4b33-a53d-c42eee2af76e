-- OBS VST插件智能参数控制器 (多参数名称尝试版)
obs = obslua

-- 配置存储
local settings = {
    source_name = "",
    filter_name = "",
    control_type = "pitch",  -- pitch, distortion, reverb, auto
    interval_min = 2.0,
    interval_max = 5.0,
    active = false,
    debug = true,
    -- 参数范围设置
    pitch_min = -6.0,
    pitch_max = 6.0,
    drive_min = 20.0,
    drive_max = 80.0,
    reverb_min = 10.0,
    reverb_max = 60.0
}

local last_update = 0

-- 日志函数
local function log(message, level)
    level = level or obs.LOG_INFO
    if settings.debug then
        obs.script_log(level, "[VST智能控制] " .. tostring(message))
    end
end

-- VST参数名称数据库 - 包含各种可能的参数名称
local VST_PARAM_DATABASE = {
    -- 音调/变声插件参数名称 (Graillon, Autotune, 等)
    pitch = {
        "pitch", "Pitch", "PITCH",
        "tune", "Tune", "TUNE", 
        "correction", "Correction", "CORRECTION",
        "shift", "Shift", "SHIFT",
        "transpose", "Transpose", "TRANSPOSE",
        "semitones", "Semitones", "SEMITONES",
        "cents", "Cents", "CENTS",
        "detune", "Detune", "DETUNE",
        "formant", "Formant", "FORMANT",
        "key", "Key", "KEY"
    },
    
    -- 失真/过载插件参数名称 (TSE808, Guitar Rig, 等)
    distortion = {
        "drive", "Drive", "DRIVE",
        "gain", "Gain", "GAIN",
        "overdrive", "Overdrive", "OVERDRIVE",
        "distortion", "Distortion", "DISTORTION",
        "saturation", "Saturation", "SATURATION",
        "intensity", "Intensity", "INTENSITY",
        "amount", "Amount", "AMOUNT",
        "level", "Level", "LEVEL",
        "input", "Input", "INPUT",
        "output", "Output", "OUTPUT"
    },
    
    -- 音色/EQ参数名称
    tone = {
        "tone", "Tone", "TONE",
        "eq", "EQ", "Eq",
        "treble", "Treble", "TREBLE",
        "bass", "Bass", "BASS",
        "mid", "Mid", "MID",
        "high", "High", "HIGH",
        "low", "Low", "LOW",
        "color", "Color", "COLOR",
        "timbre", "Timbre", "TIMBRE"
    },
    
    -- 混响插件参数名称 (TAL-Reverb, Valhalla, 等)
    reverb_size = {
        "roomsize", "RoomSize", "ROOMSIZE",
        "size", "Size", "SIZE",
        "room", "Room", "ROOM",
        "space", "Space", "SPACE",
        "hall", "Hall", "HALL",
        "decay", "Decay", "DECAY",
        "length", "Length", "LENGTH"
    },
    
    reverb_mix = {
        "mix", "Mix", "MIX",
        "wet", "Wet", "WET",
        "dry", "Dry", "DRY",
        "blend", "Blend", "BLEND",
        "balance", "Balance", "BALANCE",
        "send", "Send", "SEND",
        "return", "Return", "RETURN"
    },
    
    -- 通用控制参数
    bypass = {
        "bypass", "Bypass", "BYPASS",
        "enabled", "Enabled", "ENABLED",
        "on", "On", "ON",
        "active", "Active", "ACTIVE",
        "mute", "Mute", "MUTE"
    }
}

-- 智能参数设置函数 - 尝试多种参数名称
local function smart_set_vst_parameter(source_name, filter_name, param_category, value)
    local source = obs.obs_get_source_by_name(source_name)
    if not source then
        log("音频源不存在: " .. tostring(source_name), obs.LOG_WARNING)
        return false
    end

    local filter = obs.obs_source_get_filter_by_name(source, filter_name)
    if not filter then
        log("VST滤镜不存在: " .. tostring(filter_name), obs.LOG_WARNING)
        obs.obs_source_release(source)
        return false
    end

    local filter_settings = obs.obs_source_get_settings(filter)
    local success_count = 0
    local param_names = VST_PARAM_DATABASE[param_category] or {}
    
    log(string.format("尝试设置 %s 参数，共 %d 种可能的名称", param_category, #param_names))
    
    -- 尝试所有可能的参数名称
    for i, param_name in ipairs(param_names) do
        -- 检查参数是否存在
        if obs.obs_data_has_user_value(filter_settings, param_name) then
            log(string.format("✅ 发现参数: %s", param_name))
        end
        
        -- 尝试设置参数 (使用多种数据类型)
        local old_settings = obs.obs_source_get_settings(filter)
        
        -- 尝试 double 类型
        obs.obs_data_set_double(filter_settings, param_name, value)
        obs.obs_source_update(filter, filter_settings)
        
        -- 尝试 int 类型 (对于某些插件)
        obs.obs_data_set_int(filter_settings, param_name, math.floor(value))
        obs.obs_source_update(filter, filter_settings)
        
        -- 尝试 string 类型 (对于某些特殊插件)
        obs.obs_data_set_string(filter_settings, param_name, tostring(value))
        obs.obs_source_update(filter, filter_settings)
        
        success_count = success_count + 1
        
        obs.obs_data_release(old_settings)
    end
    
    log(string.format("参数设置完成: %s = %.2f (尝试了 %d 种名称)", param_category, value, success_count))
    
    -- 清理资源
    obs.obs_data_release(filter_settings)
    obs.obs_source_release(filter)
    obs.obs_source_release(source)
    
    return success_count > 0
end

-- 音调控制
local function control_pitch()
    local pitch_value = math.random() * (settings.pitch_max - settings.pitch_min) + settings.pitch_min
    
    local success = smart_set_vst_parameter(
        settings.source_name, 
        settings.filter_name, 
        "pitch", 
        pitch_value
    )
    
    if success then
        log(string.format("🎵 音调调整: %.1f 半音", pitch_value))
    else
        log("❌ 音调调整失败", obs.LOG_WARNING)
    end
end

-- 失真控制
local function control_distortion()
    -- 失真强度
    local drive_value = math.random() * (settings.drive_max - settings.drive_min) + settings.drive_min
    local drive_normalized = drive_value / 100.0  -- 转换为 0.0-1.0
    
    smart_set_vst_parameter(settings.source_name, settings.filter_name, "distortion", drive_normalized)
    
    -- 音色调节
    local tone_value = math.random() * 60 + 20  -- 20-80%
    local tone_normalized = tone_value / 100.0
    
    smart_set_vst_parameter(settings.source_name, settings.filter_name, "tone", tone_normalized)
    
    log(string.format("🔥 失真调整: 强度=%.1f%%, 音色=%.1f%%", drive_value, tone_value))
end

-- 混响控制
local function control_reverb()
    -- 房间大小
    local room_value = math.random() * (settings.reverb_max - settings.reverb_min) + settings.reverb_min
    local room_normalized = room_value / 100.0
    
    smart_set_vst_parameter(settings.source_name, settings.filter_name, "reverb_size", room_normalized)
    
    -- 干湿比
    local mix_value = math.random() * 40 + 10  -- 10-50%
    local mix_normalized = mix_value / 100.0
    
    smart_set_vst_parameter(settings.source_name, settings.filter_name, "reverb_mix", mix_normalized)
    
    log(string.format("🌊 混响调整: 房间=%.1f%%, 混合=%.1f%%", room_value, mix_value))
end

-- 自动识别插件类型并控制
local function auto_control()
    local filter_name_lower = string.lower(settings.filter_name or "")
    
    -- 根据滤镜名称自动判断插件类型
    if string.find(filter_name_lower, "graillon") or 
       string.find(filter_name_lower, "autotune") or
       string.find(filter_name_lower, "pitch") or
       string.find(filter_name_lower, "tune") then
        control_pitch()
    elseif string.find(filter_name_lower, "tse") or 
           string.find(filter_name_lower, "808") or
           string.find(filter_name_lower, "drive") or
           string.find(filter_name_lower, "distort") then
        control_distortion()
    elseif string.find(filter_name_lower, "reverb") or 
           string.find(filter_name_lower, "tal") or
           string.find(filter_name_lower, "hall") then
        control_reverb()
    else
        -- 未知插件，尝试通用控制
        log("🤖 未识别插件类型，尝试通用控制")
        control_pitch()  -- 默认尝试音调控制
    end
end

-- 获取音频源列表
local function get_audio_sources()
    local sources = {}
    local all_sources = obs.obs_enum_sources()
    if all_sources then
        for _, src in ipairs(all_sources) do
            local name = obs.obs_source_get_name(src)
            table.insert(sources, name)
            obs.obs_source_release(src)
        end
    end
    return sources
end

-- 脚本属性界面
function script_properties()
    local props = obs.obs_properties_create()

    -- 音频源选择
    local source_list = obs.obs_properties_add_list(props, "source_name", "🎤 选择音频源",
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)

    for _, name in ipairs(get_audio_sources()) do
        obs.obs_property_list_add_string(source_list, name, name)
    end

    -- VST滤镜名称
    obs.obs_properties_add_text(props, "filter_name", "🎛️ VST滤镜名称", obs.OBS_TEXT_DEFAULT)

    -- 控制类型选择
    local control_type_list = obs.obs_properties_add_list(props, "control_type", "🎯 控制类型",
        obs.OBS_COMBO_TYPE_LIST, obs.OBS_COMBO_FORMAT_STRING)
    obs.obs_property_list_add_string(control_type_list, "自动识别", "auto")
    obs.obs_property_list_add_string(control_type_list, "音调控制", "pitch")
    obs.obs_property_list_add_string(control_type_list, "失真控制", "distortion")
    obs.obs_property_list_add_string(control_type_list, "混响控制", "reverb")

    -- 时间间隔设置
    obs.obs_properties_add_float(props, "interval_min", "⏱️ 最小间隔(秒)", 0.5, 30.0, 0.1)
    obs.obs_properties_add_float(props, "interval_max", "⏱️ 最大间隔(秒)", 0.5, 30.0, 0.1)

    -- 音调参数范围
    obs.obs_properties_add_float(props, "pitch_min", "🎵 音调最小值(半音)", -24.0, 24.0, 0.1)
    obs.obs_properties_add_float(props, "pitch_max", "🎵 音调最大值(半音)", -24.0, 24.0, 0.1)

    -- 失真参数范围
    obs.obs_properties_add_float(props, "drive_min", "🔥 失真最小值(%)", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float(props, "drive_max", "🔥 失真最大值(%)", 0.0, 100.0, 1.0)

    -- 混响参数范围
    obs.obs_properties_add_float(props, "reverb_min", "🌊 混响最小值(%)", 0.0, 100.0, 1.0)
    obs.obs_properties_add_float(props, "reverb_max", "🌊 混响最大值(%)", 0.0, 100.0, 1.0)

    -- 控制开关
    obs.obs_properties_add_bool(props, "active", "✅ 启用VST智能控制")

    -- 调试开关
    obs.obs_properties_add_bool(props, "debug", "🐛 启用调试日志")

    return props
end

-- 更新设置
function script_update(new_settings)
    settings.source_name = obs.obs_data_get_string(new_settings, "source_name")
    settings.filter_name = obs.obs_data_get_string(new_settings, "filter_name")
    settings.control_type = obs.obs_data_get_string(new_settings, "control_type")
    settings.interval_min = obs.obs_data_get_double(new_settings, "interval_min")
    settings.interval_max = obs.obs_data_get_double(new_settings, "interval_max")
    settings.pitch_min = obs.obs_data_get_double(new_settings, "pitch_min")
    settings.pitch_max = obs.obs_data_get_double(new_settings, "pitch_max")
    settings.drive_min = obs.obs_data_get_double(new_settings, "drive_min")
    settings.drive_max = obs.obs_data_get_double(new_settings, "drive_max")
    settings.reverb_min = obs.obs_data_get_double(new_settings, "reverb_min")
    settings.reverb_max = obs.obs_data_get_double(new_settings, "reverb_max")
    settings.active = obs.obs_data_get_bool(new_settings, "active")
    settings.debug = obs.obs_data_get_bool(new_settings, "debug")

    -- 确保范围有效
    if settings.pitch_min > settings.pitch_max then
        settings.pitch_min, settings.pitch_max = settings.pitch_max, settings.pitch_min
    end
    if settings.drive_min > settings.drive_max then
        settings.drive_min, settings.drive_max = settings.drive_max, settings.drive_min
    end
    if settings.reverb_min > settings.reverb_max then
        settings.reverb_min, settings.reverb_max = settings.reverb_max, settings.reverb_min
    end

    log("设置已更新")
end

-- 主控制逻辑
local function update_vst_parameters()
    if not settings.source_name or settings.source_name == "" then
        log("未选择音频源", obs.LOG_WARNING)
        return
    end

    if not settings.filter_name or settings.filter_name == "" then
        log("未设置VST滤镜名称", obs.LOG_WARNING)
        return
    end

    log("开始VST参数控制...")

    -- 根据控制类型执行相应操作
    if settings.control_type == "pitch" then
        control_pitch()
    elseif settings.control_type == "distortion" then
        control_distortion()
    elseif settings.control_type == "reverb" then
        control_reverb()
    else
        auto_control()  -- 自动识别或默认
    end

    -- 设置下次更新时间
    local next_interval = math.random() * (settings.interval_max - settings.interval_min) + settings.interval_min
    last_update = os.clock() + next_interval
    log(string.format("下次更新将在 %.1f 秒后", next_interval))
end

-- 主循环
function script_tick(seconds)
    if settings.active and os.clock() >= last_update then
        update_vst_parameters()
    end
end

-- 脚本描述
function script_description()
    return [[🎛️ VST智能参数控制器 (多参数名称支持版)

🚀 核心特性：
✅ 智能参数名称识别 - 支持数十种可能的参数名称
✅ 多插件类型支持 - 音调/失真/混响自动识别
✅ 参数范围自定义 - 精确控制效果强度
✅ 多数据类型尝试 - double/int/string全覆盖
✅ 详细调试日志 - 实时监控参数设置状态

🎯 支持的插件类型：
🎵 音调插件: Graillon, Autotune, 各种变声器
🔥 失真插件: TSE808, Guitar Rig, 各种过载器
🌊 混响插件: TAL-Reverb, Valhalla, 各种空间效果

📋 使用步骤：
1. 选择包含VST滤镜的音频源
2. 输入VST滤镜的确切名称
3. 选择控制类型(推荐使用"自动识别")
4. 调整参数范围和更新间隔
5. 启用智能控制

💡 智能特性：
- 自动尝试20+种音调参数名称
- 自动尝试15+种失真参数名称
- 自动尝试10+种混响参数名称
- 根据滤镜名称自动识别插件类型
- 多种数据类型同时尝试确保兼容性

⚠️ 注意事项：
- 滤镜名称必须与OBS中显示的完全一致
- 建议先启用调试日志查看参数设置情况
- 某些插件可能需要特殊的参数格式]]
end
