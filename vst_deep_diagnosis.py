#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔬 VST深度诊断工具
全面诊断VST参数控制问题
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
import base64
import struct
from datetime import datetime

class VSTDeepDiagnosis:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔬 VST深度诊断工具")
        self.root.geometry("1000x700")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 诊断数据
        self.baseline_chunk = None
        self.current_chunk = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST信息区域
        info_frame = ttk.LabelFrame(main_frame, text="🎛️ VST插件信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_row = ttk.Frame(info_frame)
        info_row.pack(fill=tk.X)
        
        ttk.Label(info_row, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(info_row, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(info_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(info_row, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # 诊断区域
        diag_frame = ttk.LabelFrame(main_frame, text="🔬 深度诊断", padding="10")
        diag_frame.pack(fill=tk.X, pady=(0, 10))
        
        btn_row1 = ttk.Frame(diag_frame)
        btn_row1.pack(fill=tk.X, pady=(0, 5))
        
        self.basic_check_btn = ttk.Button(btn_row1, text="🔍 基础检查", 
                                         command=self.basic_check, state="disabled")
        self.basic_check_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.chunk_analysis_btn = ttk.Button(btn_row1, text="🧬 Chunk分析", 
                                           command=self.analyze_chunk_data, state="disabled")
        self.chunk_analysis_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.manual_control_btn = ttk.Button(btn_row1, text="🎛️ 手动控制测试", 
                                           command=self.manual_control_test, state="disabled")
        self.manual_control_btn.pack(side=tk.LEFT)
        
        btn_row2 = ttk.Frame(diag_frame)
        btn_row2.pack(fill=tk.X)
        
        self.bypass_test_btn = ttk.Button(btn_row2, text="🔄 Bypass测试", 
                                         command=self.test_bypass, state="disabled")
        self.bypass_test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.extreme_test_btn = ttk.Button(btn_row2, text="⚡ 极端值测试", 
                                          command=self.extreme_value_test, state="disabled")
        self.extreme_test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.plugin_info_btn = ttk.Button(btn_row2, text="📋 插件信息", 
                                         command=self.get_plugin_info, state="disabled")
        self.plugin_info_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 诊断结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🔬 VST深度诊断工具启动")
        self.log("这个工具将全面诊断VST参数控制问题")
        self.log("可能的问题:")
        self.log("1. VST插件没有真正加载")
        self.log("2. 参数映射问题")
        self.log("3. 音频路由问题")
        self.log("4. VST插件内部状态问题")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有按钮
        buttons = [self.basic_check_btn, self.chunk_analysis_btn, self.manual_control_btn,
                  self.bypass_test_btn, self.extreme_test_btn, self.plugin_info_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("💡 开始深度诊断")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.basic_check_btn, self.chunk_analysis_btn, self.manual_control_btn,
                  self.bypass_test_btn, self.extreme_test_btn, self.plugin_info_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
    
    def basic_check(self):
        """基础检查"""
        self.log("🔍 开始基础检查...")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 1. 检查音频源是否存在
        self.log("1️⃣ 检查音频源...")
        sources_response = self.send_request("GetInputList")
        if sources_response and sources_response.get('requestStatus', {}).get('result'):
            sources = sources_response.get('responseData', {}).get('inputs', [])
            source_names = [s.get('inputName') for s in sources]

            if source_name in source_names:
                self.log(f"   ✅ 音频源 '{source_name}' 存在")
            else:
                self.log(f"   ❌ 音频源 '{source_name}' 不存在")
                self.log(f"   📋 可用音频源: {source_names}")
                return
        else:
            self.log("   ❌ 获取音频源列表失败")
            return

        # 2. 检查滤镜是否存在
        self.log("2️⃣ 检查VST滤镜...")
        filters_response = self.send_request("GetSourceFilterList", {
            "sourceName": source_name
        })

        if filters_response and filters_response.get('requestStatus', {}).get('result'):
            filters = filters_response.get('responseData', {}).get('filters', [])
            filter_names = [f.get('filterName') for f in filters]

            if filter_name in filter_names:
                self.log(f"   ✅ VST滤镜 '{filter_name}' 存在")

                # 检查滤镜详细信息
                for f in filters:
                    if f.get('filterName') == filter_name:
                        self.log(f"   📊 滤镜类型: {f.get('filterKind')}")
                        self.log(f"   📊 滤镜启用: {f.get('filterEnabled')}")
                        break
            else:
                self.log(f"   ❌ VST滤镜 '{filter_name}' 不存在")
                self.log(f"   📋 可用滤镜: {filter_names}")
                return
        else:
            self.log("   ❌ 获取滤镜列表失败")
            return

        # 3. 检查VST插件是否真正加载
        self.log("3️⃣ 检查VST插件状态...")
        filter_response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if filter_response and filter_response.get('requestStatus', {}).get('result'):
            filter_data = filter_response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            # 检查是否有plugin_path
            plugin_path = settings.get('plugin_path', '')
            if plugin_path:
                self.log(f"   ✅ VST插件路径: {plugin_path}")
            else:
                self.log("   ❌ 未找到VST插件路径")

            # 检查chunk_data
            chunk_data = settings.get('chunk_data', '')
            if chunk_data:
                self.log(f"   ✅ VST插件有状态数据 (长度: {len(chunk_data)})")
                self.baseline_chunk = chunk_data
            else:
                self.log("   ❌ VST插件没有状态数据")

            # 检查参数数量
            param_count = len([k for k in settings.keys() if k not in ['plugin_path', 'chunk_data', 'chunk_hash']])
            self.log(f"   📊 可控制参数数量: {param_count}")

        else:
            self.log("   ❌ 获取VST滤镜详细信息失败")
            return

        # 4. 检查音频是否在播放
        self.log("4️⃣ 检查音频状态...")
        # 这里我们无法直接检查音频，但可以提醒用户
        self.log("   💡 请确保:")
        self.log("      - OBS中有音频输入")
        self.log("      - 音频源正在播放")
        self.log("      - 音频监控已开启")

        self.log("=" * 50)
        self.log("✅ 基础检查完成")

    def test_bypass(self):
        """测试Bypass功能"""
        self.log("🔄 测试VST插件Bypass功能...")

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 测试启用/禁用滤镜
        for state in [False, True, False]:
            state_text = "禁用" if not state else "启用"
            self.log(f"   🔄 {state_text}VST滤镜...")

            response = self.send_request("SetSourceFilterEnabled", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterEnabled": state
            })

            if response and response.get('requestStatus', {}).get('result'):
                self.log(f"      ✅ {state_text}成功")
                self.log(f"      🎧 请听音频是否有变化...")
                time.sleep(3)
            else:
                self.log(f"      ❌ {state_text}失败")

        # 测试bypass参数
        if 'bypass' in self.get_current_params():
            self.log("   🔄 测试bypass参数...")
            for bypass_val in [1.0, 0.0]:
                bypass_text = "开启" if bypass_val else "关闭"
                self.log(f"      🔄 {bypass_text}bypass...")

                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        "bypass": bypass_val
                    }
                })

                if response and response.get('requestStatus', {}).get('result'):
                    self.log(f"         ✅ {bypass_text}成功")
                    time.sleep(2)
                else:
                    self.log(f"         ❌ {bypass_text}失败")

        self.log("🔄 Bypass测试完成")

    def get_current_params(self):
        """获取当前参数"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})
            return {k: v for k, v in settings.items() if k not in ['plugin_path', 'chunk_data', 'chunk_hash']}
        return {}

    def manual_control_test(self):
        """手动控制测试"""
        self.log("🎛️ 手动控制测试...")
        self.log("这个测试将指导您手动验证VST插件是否工作")
        self.log("=" * 50)

        self.log("📋 请按以下步骤操作:")
        self.log("1. 在OBS中右键点击音频源")
        self.log("2. 选择'滤镜'")
        self.log("3. 双击VST滤镜打开插件界面")
        self.log("4. 手动调节音调相关的滑块/旋钮")
        self.log("5. 观察是否有音调变化")

        self.log("\n💡 如果手动调节有效果:")
        self.log("   - VST插件工作正常")
        self.log("   - 问题在于参数映射")
        self.log("   - 需要找到正确的参数名称")

        self.log("\n💡 如果手动调节无效果:")
        self.log("   - VST插件可能没有正确加载")
        self.log("   - 音频路由可能有问题")
        self.log("   - 需要检查音频设置")

        # 等待用户测试
        self.log("\n⏳ 请现在进行手动测试...")
        self.log("测试完成后，点击其他诊断按钮继续")

    def extreme_value_test(self):
        """极端值测试"""
        self.log("⚡ 极端值测试...")
        self.log("使用极端数值来测试参数是否真的有效")

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 获取当前参数
        current_params = self.get_current_params()

        if not current_params:
            self.log("❌ 无法获取当前参数")
            return

        # 极端值测试
        extreme_tests = [
            # 音调相关参数的极端值
            ("pitch", [-50.0, 0.0, 50.0]),
            ("pitch_shift", [-50.0, 0.0, 50.0]),
            ("pitch_shift_amount", [-50.0, 0.0, 50.0]),
            ("transpose", [-24, 0, 24]),
            ("coarse", [-24, 0, 24]),
            ("fine", [-100, 0, 100]),
            ("tune", [0.0, 50.0, 100.0]),

            # 其他可能影响音调的参数
            ("formant", [0.0, 100.0, 200.0]),
            ("formant_shift", [0.5, 1.0, 2.0]),
            ("voice_pitch", [0.5, 1.0, 2.0]),
        ]

        for param_name, test_values in extreme_tests:
            if param_name not in current_params:
                continue

            original_value = current_params[param_name]
            self.log(f"\n🧪 极端测试: {param_name}")
            self.log(f"   原始值: {original_value}")

            for test_value in test_values:
                self.log(f"   ⚡ 设置极端值: {test_value}")

                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                }, timeout=3)

                if response and response.get('requestStatus', {}).get('result'):
                    self.log(f"      ✅ 设置成功")
                    self.log(f"      🎧 请听音频变化... (等待2秒)")
                    time.sleep(2)

                    # 验证是否真的设置了
                    verify_response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=2)

                    if verify_response and verify_response.get('requestStatus', {}).get('result'):
                        verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                        if param_name in verify_settings:
                            actual_value = verify_settings[param_name]
                            try:
                                if abs(float(actual_value) - test_value) < 0.01:
                                    self.log(f"      ✅ 验证成功: {actual_value}")
                                else:
                                    self.log(f"      ⚠️ 值不匹配: 期望{test_value}, 实际{actual_value}")
                            except:
                                self.log(f"      📝 实际值: {actual_value}")
                else:
                    self.log(f"      ❌ 设置失败")

            # 恢复原始值
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: original_value
                }
            }, timeout=2)

        self.log("\n⚡ 极端值测试完成")
        self.log("💡 如果没有听到任何变化，可能的原因:")
        self.log("   1. VST插件没有真正处理音频")
        self.log("   2. 音频路由配置错误")
        self.log("   3. 参数名称映射错误")

    def get_plugin_info(self):
        """获取插件详细信息"""
        self.log("📋 获取VST插件详细信息...")

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.log("=" * 50)
            self.log("📊 VST插件完整信息:")

            # 插件路径
            plugin_path = settings.get('plugin_path', '未知')
            self.log(f"🔌 插件路径: {plugin_path}")

            # Chunk数据分析
            chunk_data = settings.get('chunk_data', '')
            if chunk_data:
                self.log(f"🧬 状态数据长度: {len(chunk_data)} 字符")
                try:
                    decoded_data = base64.b64decode(chunk_data)
                    self.log(f"🧬 解码后长度: {len(decoded_data)} 字节")

                    # 尝试解析为float数组
                    if len(decoded_data) >= 4:
                        float_count = len(decoded_data) // 4
                        self.log(f"🧬 可能的参数数量: {float_count}")

                        # 显示前10个float值
                        self.log("🧬 前10个可能的参数值:")
                        for i in range(min(10, float_count)):
                            try:
                                offset = i * 4
                                float_val = struct.unpack('<f', decoded_data[offset:offset+4])[0]
                                self.log(f"   参数 {i:2d}: {float_val:8.3f}")
                            except:
                                break
                except Exception as e:
                    self.log(f"❌ 解析chunk数据失败: {e}")

            # 所有参数
            self.log("\n📊 所有可控制参数:")
            param_count = 0
            for key, value in settings.items():
                if key not in ['plugin_path', 'chunk_data', 'chunk_hash']:
                    param_count += 1
                    try:
                        num_val = float(value)
                        self.log(f"   📊 {key}: {num_val:.6f}")
                    except:
                        self.log(f"   📝 {key}: {value}")

            self.log(f"\n📊 总参数数量: {param_count}")
            self.log("=" * 50)
        else:
            self.log("❌ 获取插件信息失败")

    def analyze_chunk_data(self):
        """分析Chunk数据变化"""
        self.log("🧬 分析VST插件内部状态变化...")

        if not self.baseline_chunk:
            self.log("⚠️ 请先运行'基础检查'获取基准状态")
            return

        self.log("📊 记录当前状态...")
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            settings = response.get('responseData', {}).get('filterSettings', {})
            current_chunk = settings.get('chunk_data', '')

            if current_chunk and current_chunk != self.baseline_chunk:
                self.log("✅ 检测到chunk数据变化！")

                try:
                    baseline_data = base64.b64decode(self.baseline_chunk)
                    current_data = base64.b64decode(current_chunk)

                    if len(baseline_data) == len(current_data):
                        changes_found = 0
                        float_count = len(baseline_data) // 4

                        self.log("🔍 分析内部参数变化:")
                        for i in range(float_count):
                            offset = i * 4
                            try:
                                baseline_val = struct.unpack('<f', baseline_data[offset:offset+4])[0]
                                current_val = struct.unpack('<f', current_data[offset:offset+4])[0]

                                if abs(baseline_val - current_val) > 0.0001:
                                    self.log(f"   🔄 内部参数 {i:2d}: {baseline_val:8.6f} → {current_val:8.6f}")
                                    changes_found += 1
                            except:
                                continue

                        if changes_found == 0:
                            self.log("ℹ️ 未检测到内部参数变化")
                        else:
                            self.log(f"✅ 检测到 {changes_found} 个内部参数变化")
                            self.log("💡 这说明VST插件确实在响应参数变化")
                    else:
                        self.log("⚠️ chunk数据长度不一致")

                except Exception as e:
                    self.log(f"❌ 分析chunk数据失败: {e}")

                # 更新基准
                self.baseline_chunk = current_chunk
            else:
                self.log("ℹ️ chunk数据没有变化")
                self.log("💡 这可能意味着:")
                self.log("   1. VST插件没有响应参数变化")
                self.log("   2. 参数变化太小，没有影响内部状态")
                self.log("   3. 需要更大幅度的参数变化")
        else:
            self.log("❌ 获取当前状态失败")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTDeepDiagnosis()
    app.run()
