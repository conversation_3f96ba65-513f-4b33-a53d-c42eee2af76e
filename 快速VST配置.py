#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速VST配置工具
帮助你快速找到正确的音频源和VST滤镜名称
"""

import obsws_python as obs
import sys

def connect_obs(password=""):
    """连接到OBS"""
    try:
        client = obs.ReqClient(host="localhost", port=4455, password=password)
        print("✅ 成功连接到OBS WebSocket")
        return client
    except Exception as e:
        print(f"❌ 连接OBS失败: {e}")
        print("\n🔧 请检查:")
        print("1. OBS是否正在运行")
        print("2. WebSocket服务器是否已启用")
        print("   - 打开OBS → 工具 → WebSocket服务器设置")
        print("   - 启用服务器，端口设为4455")
        print("3. 密码是否正确")
        return None

def list_audio_sources(client):
    """列出所有音频源"""
    try:
        response = client.get_scene_list()
        current_scene = response.current_program_scene_name
        
        print(f"🎬 当前场景: {current_scene}")
        
        scene_items = client.get_scene_item_list(scene_name=current_scene)
        
        print(f"\n🎤 发现的音频源:")
        print("=" * 50)
        
        audio_sources = []
        for i, item in enumerate(scene_items.scene_items, 1):
            source_name = item['sourceName']
            source_type = item.get('sourceType', '未知')
            
            # 获取源的详细信息
            try:
                source_info = client.get_input_settings(input_name=source_name)
                print(f"[{i:2d}] 📻 {source_name}")
                print(f"     类型: {source_type}")
                
                # 检查是否有音频轨道
                try:
                    audio_settings = client.get_input_audio_settings(input_name=source_name)
                    print(f"     音频: ✅ 有音频轨道")
                    audio_sources.append(source_name)
                except:
                    print(f"     音频: ❌ 无音频轨道")
                    
            except Exception as e:
                print(f"[{i:2d}] 📻 {source_name} (无法获取详细信息)")
                # 仍然添加到列表中，可能是音频源
                audio_sources.append(source_name)
            
            print()
        
        return audio_sources
        
    except Exception as e:
        print(f"❌ 获取音频源失败: {e}")
        return []

def list_vst_filters(client, source_name):
    """列出指定音频源的所有VST滤镜"""
    try:
        response = client.get_source_filter_list(source_name=source_name)
        filters = response.filters
        
        print(f"🎛️ {source_name} 的滤镜:")
        print("=" * 50)
        
        vst_filters = []
        for i, filter_info in enumerate(filters, 1):
            filter_name = filter_info['filterName']
            filter_type = filter_info['filterKind']
            filter_enabled = filter_info['filterEnabled']
            
            status = "✅ 启用" if filter_enabled else "❌ 禁用"
            
            print(f"[{i:2d}] 🎚️ {filter_name}")
            print(f"     类型: {filter_type}")
            print(f"     状态: {status}")
            
            # 检查是否是VST滤镜
            if 'vst' in filter_type.lower() or 'plugin' in filter_type.lower():
                print(f"     🎯 这是VST插件!")
                vst_filters.append(filter_name)
            
            print()
        
        return vst_filters
        
    except Exception as e:
        print(f"❌ 获取滤镜列表失败: {e}")
        return []

def quick_test_vst(client, source_name, filter_name):
    """快速测试VST滤镜是否可控制"""
    print(f"🧪 快速测试 {source_name} → {filter_name}")
    print("=" * 50)
    
    try:
        # 获取当前设置
        response = client.get_source_filter(source_name=source_name, filter_name=filter_name)
        current_settings = response.filter_settings
        
        print(f"📋 当前参数 (共 {len(current_settings)} 个):")
        for key, value in current_settings.items():
            print(f"  • {key}: {value} ({type(value).__name__})")
        
        # 尝试常见参数
        test_params = ["pitch", "drive", "mix", "gain", "tone"]
        successful_params = []
        
        print(f"\n🎯 测试常见参数:")
        for param in test_params:
            if param in current_settings:
                print(f"  ✅ {param}: 存在于当前设置中")
                successful_params.append(param)
            else:
                # 尝试设置看是否有效
                try:
                    client.set_source_filter_settings(
                        source_name=source_name,
                        filter_name=filter_name,
                        filter_settings={param: 0.5}
                    )
                    print(f"  🆕 {param}: 可以设置新参数")
                    successful_params.append(param)
                except:
                    print(f"  ❌ {param}: 无效参数")
        
        if successful_params:
            print(f"\n🎉 发现 {len(successful_params)} 个可控制参数!")
            print("建议使用这些参数名称进行控制。")
        else:
            print(f"\n⚠️ 未发现明显的可控制参数，可能需要更详细的分析。")
        
        return successful_params
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def main():
    """主函数"""
    print("🎛️ 快速VST配置工具")
    print("=" * 60)
    
    # 尝试连接OBS
    password = input("请输入OBS WebSocket密码 (没有密码直接回车): ").strip()
    client = connect_obs(password)
    
    if not client:
        return
    
    try:
        # 1. 列出所有音频源
        print("\n" + "="*60)
        audio_sources = list_audio_sources(client)
        
        if not audio_sources:
            print("❌ 未发现音频源")
            return
        
        # 2. 让用户选择音频源
        print(f"\n📝 请选择音频源:")
        for i, source in enumerate(audio_sources, 1):
            print(f"[{i}] {source}")
        
        try:
            choice = int(input(f"\n请输入选择 (1-{len(audio_sources)}): ")) - 1
            if 0 <= choice < len(audio_sources):
                selected_source = audio_sources[choice]
                print(f"✅ 已选择: {selected_source}")
            else:
                print("❌ 选择无效")
                return
        except ValueError:
            print("❌ 请输入数字")
            return
        
        # 3. 列出该音频源的VST滤镜
        print("\n" + "="*60)
        vst_filters = list_vst_filters(client, selected_source)
        
        if not vst_filters:
            print("❌ 该音频源没有VST滤镜")
            return
        
        # 4. 让用户选择VST滤镜
        print(f"\n📝 请选择VST滤镜:")
        for i, filter_name in enumerate(vst_filters, 1):
            print(f"[{i}] {filter_name}")
        
        try:
            choice = int(input(f"\n请输入选择 (1-{len(vst_filters)}): ")) - 1
            if 0 <= choice < len(vst_filters):
                selected_filter = vst_filters[choice]
                print(f"✅ 已选择: {selected_filter}")
            else:
                print("❌ 选择无效")
                return
        except ValueError:
            print("❌ 请输入数字")
            return
        
        # 5. 快速测试VST滤镜
        print("\n" + "="*60)
        successful_params = quick_test_vst(client, selected_source, selected_filter)
        
        # 6. 生成配置代码
        print("\n" + "="*60)
        print("💻 生成的配置代码:")
        print("=" * 30)
        
        config_code = f'''
# 在 VST参数验证工具.py 中使用这些配置:
SOURCE_NAME = "{selected_source}"
FILTER_NAME = "{selected_filter}"
WEBSOCKET_PASSWORD = "{password}"

# 在 vst_control_example.py 中使用:
controller.control_graillon_pitch("{selected_source}", "{selected_filter}", pitch_value=-3.0)
'''
        
        print(config_code)
        
        # 保存配置到文件
        with open("VST配置.txt", "w", encoding="utf-8") as f:
            f.write(f"音频源: {selected_source}\n")
            f.write(f"VST滤镜: {selected_filter}\n")
            f.write(f"WebSocket密码: {password}\n")
            f.write(f"可控制参数: {', '.join(successful_params)}\n")
            f.write(f"生成时间: {__import__('datetime').datetime.now()}\n")
        
        print("📄 配置已保存到 VST配置.txt")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
