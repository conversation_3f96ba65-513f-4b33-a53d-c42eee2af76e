#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OBS VST插件控制示例
通过OBS WebSocket API控制VST 2.x插件参数
"""

import obsws_python as obs
import time
import random

class VSTController:
    def __init__(self, host="localhost", port=4455, password=""):
        """初始化OBS WebSocket连接"""
        self.client = obs.ReqClient(host=host, port=port, password=password)
        
    def set_vst_parameter(self, source_name, filter_name, param_name, value):
        """
        设置VST插件参数
        
        Args:
            source_name: 音频源名称 (如 "麦克风")
            filter_name: VST滤镜名称 (如 "Graillon")
            param_name: 参数名称 (如 "pitch", "drive", "mix")
            value: 参数值 (通常0.0-1.0或具体数值)
        """
        try:
            # 方法1: 直接设置滤镜参数
            response = self.client.set_source_filter_settings(
                source_name=source_name,
                filter_name=filter_name,
                filter_settings={param_name: value}
            )
            print(f"✅ 设置 {filter_name}.{param_name} = {value}")
            return True
            
        except Exception as e:
            print(f"❌ 设置参数失败: {e}")
            return False
    
    def get_vst_parameters(self, source_name, filter_name):
        """获取VST插件当前参数"""
        try:
            response = self.client.get_source_filter(
                source_name=source_name,
                filter_name=filter_name
            )
            return response.filter_settings
        except Exception as e:
            print(f"❌ 获取参数失败: {e}")
            return None
    
    def control_graillon_pitch(self, source_name, filter_name="Graillon", pitch_value=0.0):
        """控制Graillon音调插件"""
        # Graillon常见参数名称
        params = {
            "pitch": pitch_value,      # 音调偏移 (-12.0 到 12.0)
            "correction": 0.5,         # 音调修正强度
            "preserve_formants": True   # 保持共振峰
        }
        
        for param, value in params.items():
            self.set_vst_parameter(source_name, filter_name, param, value)
    
    def control_tse808_distortion(self, source_name, filter_name="TSE808", drive=50.0, tone=50.0):
        """控制TSE808失真插件"""
        params = {
            "drive": drive / 100.0,    # 失真强度 (0-100转换为0.0-1.0)
            "tone": tone / 100.0,      # 音色调节
            "level": 0.8,              # 输出电平
            "bypass": False            # 是否旁通
        }
        
        for param, value in params.items():
            self.set_vst_parameter(source_name, filter_name, param, value)
    
    def control_tal_reverb(self, source_name, filter_name="TAL-Reverb", roomsize=60.0, mix=30.0):
        """控制TAL混响插件"""
        params = {
            "roomsize": roomsize / 100.0,  # 房间大小
            "mix": mix / 100.0,            # 干湿比
            "damping": 0.5,                # 阻尼
            "predelay": 0.1                # 预延迟
        }
        
        for param, value in params.items():
            self.set_vst_parameter(source_name, filter_name, param, value)

def main():
    """示例使用"""
    # 连接到OBS
    controller = VSTController(password="your_websocket_password")
    
    try:
        # 示例1: 控制音调
        print("🎵 控制Graillon音调...")
        controller.control_graillon_pitch("麦克风", "Graillon", pitch_value=-3.0)
        
        time.sleep(2)
        
        # 示例2: 控制失真
        print("🔥 控制TSE808失真...")
        controller.control_tse808_distortion("麦克风", "TSE808", drive=70.0, tone=60.0)
        
        time.sleep(2)
        
        # 示例3: 控制混响
        print("🌊 控制TAL混响...")
        controller.control_tal_reverb("麦克风", "TAL-Reverb", roomsize=80.0, mix=40.0)
        
        # 示例4: 随机控制循环
        print("🎲 开始随机控制...")
        for i in range(10):
            # 随机音调
            pitch = random.uniform(-6.0, 6.0)
            controller.control_graillon_pitch("麦克风", "Graillon", pitch)
            
            # 随机失真
            drive = random.uniform(20.0, 80.0)
            tone = random.uniform(30.0, 70.0)
            controller.control_tse808_distortion("麦克风", "TSE808", drive, tone)
            
            print(f"第{i+1}次随机调整完成")
            time.sleep(3)
            
    except KeyboardInterrupt:
        print("🛑 用户中断")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
