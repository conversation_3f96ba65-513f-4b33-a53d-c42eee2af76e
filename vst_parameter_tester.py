#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 VST参数测试器
专门测试发现的Graillon参数是否真的有效
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class VSTParameterTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 VST参数测试器 - 验证Graillon参数")
        self.root.geometry("1200x800")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0

        # 监控状态
        self.monitoring_enabled = False
        self.monitor_thread = None

        # 发现的实际参数
        self.discovered_params = {}

        # 测试参数 - 使用更合理的数值范围
        self.test_params = {
            "pitch_shift": {"current": 3.0, "test_values": [-12.0, -6.0, 0.0, 6.0, 12.0]},
            "formant_shift": {"current": 1.1, "test_values": [0.5, 0.8, 1.0, 1.2, 1.5, 2.0]},
            "correction_strength": {"current": 0.8, "test_values": [0.0, 0.3, 0.6, 1.0]},
            "dry_wet_mix": {"current": 0.0, "test_values": [0.0, 0.25, 0.5, 0.75, 1.0]},
            "pitch_correction": {"current": 0.0, "test_values": [0.0, 0.3, 0.6, 1.0]},
            "transpose": {"current": 2, "test_values": [-12, -7, -3, 0, 3, 7, 12]},
            "voice_pitch": {"current": 1.2, "test_values": [0.5, 0.8, 1.0, 1.5, 2.0, 3.0]},
            "coarse": {"current": 2, "test_values": [-12, -5, 0, 5, 12]},
            "fine": {"current": 10, "test_values": [-50, -20, 0, 20, 50, 100]},
            "tune": {"current": 50.0, "test_values": [0.0, 25.0, 50.0, 75.0, 100.0]},
            # 常见的实际参数名
            "pitch": {"current": 0.0, "test_values": [-12.0, -6.0, 0.0, 6.0, 12.0]},
            "formant": {"current": 100.0, "test_values": [50.0, 75.0, 100.0, 125.0, 150.0]},
            "mix": {"current": 100.0, "test_values": [0.0, 25.0, 50.0, 75.0, 100.0]},
        }

        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST信息区域
        info_frame = ttk.LabelFrame(main_frame, text="🎛️ VST插件信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_row = ttk.Frame(info_frame)
        info_row.pack(fill=tk.X)
        
        ttk.Label(info_row, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(info_row, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(info_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(info_row, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        self.get_current_btn = ttk.Button(info_row, text="📊 获取当前值",
                                         command=self.get_current_values, state="disabled")
        self.get_current_btn.pack(side=tk.LEFT, padx=(10, 5))

        self.discover_btn = ttk.Button(info_row, text="🔍 探测参数",
                                      command=self.discover_parameters, state="disabled")
        self.discover_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.monitor_var = tk.BooleanVar()
        self.monitor_check = ttk.Checkbutton(info_row, text="📡 实时监控",
                                           variable=self.monitor_var,
                                           command=self.toggle_monitoring, state="disabled")
        self.monitor_check.pack(side=tk.LEFT, padx=(0, 5))

        self.refresh_btn = ttk.Button(info_row, text="🔄 手动刷新",
                                     command=self.manual_refresh, state="disabled")
        self.refresh_btn.pack(side=tk.LEFT)
        
        # 参数测试区域
        test_frame = ttk.LabelFrame(main_frame, text="🧪 参数测试", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：单个参数测试
        row1 = ttk.Frame(test_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="测试参数:").pack(side=tk.LEFT)
        self.param_combo = ttk.Combobox(row1, values=list(self.test_params.keys()), 
                                       state="readonly", width=15)
        self.param_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.param_combo.set("pitch_shift")
        
        ttk.Label(row1, text="测试值:").pack(side=tk.LEFT)
        self.test_value_var = tk.StringVar(value="5.0")
        ttk.Entry(row1, textvariable=self.test_value_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        self.test_single_btn = ttk.Button(row1, text="🎯 测试单个参数", 
                                         command=self.test_single_parameter, state="disabled")
        self.test_single_btn.pack(side=tk.LEFT, padx=(10, 5))
        
        self.reset_btn = ttk.Button(row1, text="🔄 重置参数", 
                                   command=self.reset_parameter, state="disabled")
        self.reset_btn.pack(side=tk.LEFT)
        
        # 第二行：批量测试
        row2 = ttk.Frame(test_frame)
        row2.pack(fill=tk.X)
        
        self.test_all_btn = ttk.Button(row2, text="🚀 测试所有关键参数", 
                                      command=self.test_all_parameters, state="disabled")
        self.test_all_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.demo_btn = ttk.Button(row2, text="🎭 演示效果", 
                                  command=self.demo_effects, state="disabled")
        self.demo_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.restore_btn = ttk.Button(row2, text="🏠 恢复默认",
                                     command=self.restore_defaults, state="disabled")
        self.restore_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.extreme_test_btn = ttk.Button(row2, text="⚡ 极端值测试",
                                          command=self.test_extreme_values, state="disabled")
        self.extreme_test_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.smart_test_btn = ttk.Button(row2, text="🧠 智能测试",
                                        command=self.smart_test_discovered, state="disabled")
        self.smart_test_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🧪 VST参数测试器启动")
        self.log("这个工具将验证发现的Graillon参数是否真的有效")
        self.log("请先连接OBS，然后开始测试")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有测试按钮
        self.get_current_btn.config(state="normal")
        self.discover_btn.config(state="normal")
        self.monitor_check.config(state="normal")
        self.refresh_btn.config(state="normal")
        self.test_single_btn.config(state="normal")
        self.reset_btn.config(state="normal")
        self.test_all_btn.config(state="normal")
        self.demo_btn.config(state="normal")
        self.restore_btn.config(state="normal")
        self.extreme_test_btn.config(state="normal")
        self.smart_test_btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("💡 建议先点击'获取当前值'查看VST插件的当前状态")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 停止监控
        if self.monitoring_enabled:
            self.stop_monitoring()

        # 禁用所有测试按钮
        buttons = [self.get_current_btn, self.discover_btn, self.refresh_btn, self.test_single_btn,
                  self.reset_btn, self.test_all_btn, self.demo_btn, self.restore_btn,
                  self.extreme_test_btn, self.smart_test_btn]
        for btn in buttons:
            btn.config(state="disabled")
        self.monitor_check.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
            
    def get_current_values(self):
        """获取当前参数值"""
        # 检查连接状态
        if not self.is_connected:
            self.log("❌ 请先连接OBS！")
            messagebox.showwarning("连接错误", "请先点击'连接OBS'按钮连接到OBS")
            return

        source_name = self.source_var.get().strip()
        filter_name = self.filter_var.get().strip()

        # 检查输入
        if not source_name or not filter_name:
            self.log("❌ 请填写音频源名称和VST滤镜名称！")
            messagebox.showwarning("输入错误", "请确保音频源和VST滤镜名称都已填写")
            return

        self.log("📊 开始基础检查...")
        self.log(f"🎯 目标音频源: {source_name}")
        self.log(f"🎛️ 目标VST滤镜: {filter_name}")
        self.log("-" * 50)

        # 首先检查音频源是否存在
        self.log("🔍 步骤1: 检查音频源是否存在...")
        source_response = self.send_request("GetInputList")

        if source_response and source_response.get('requestStatus', {}).get('result'):
            inputs = source_response.get('responseData', {}).get('inputs', [])
            source_names = [inp.get('inputName', '') for inp in inputs]

            if source_name in source_names:
                self.log(f"✅ 找到音频源: {source_name}")
            else:
                self.log(f"❌ 未找到音频源: {source_name}")
                self.log("📋 可用的音频源:")
                for name in source_names[:10]:  # 只显示前10个
                    self.log(f"   - {name}")
                if len(source_names) > 10:
                    self.log(f"   ... 还有 {len(source_names) - 10} 个")
                return
        else:
            self.log("❌ 无法获取音频源列表")
            return

        # 检查VST滤镜
        self.log("🔍 步骤2: 检查VST滤镜是否存在...")
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.log(f"✅ 找到VST滤镜: {filter_name}")
            self.log("📊 步骤3: 分析当前参数...")
            self.log("-" * 40)

            # 显示所有参数
            param_count = 0
            for key, value in settings.items():
                if key != 'plugin_path':  # 跳过插件路径
                    param_count += 1
                    # 检查是否在预设参数中
                    if key in self.test_params:
                        self.test_params[key]["current"] = value
                        self.log(f"  ✅ {key}: {value} (预设参数)")
                    else:
                        self.log(f"  📊 {key}: {value} (发现的参数)")

            self.log("-" * 40)
            self.log(f"🎯 基础检查完成！共发现 {param_count} 个参数")
            self.log("💡 建议下一步:")
            self.log("   1. 点击'探测参数'获取完整参数列表")
            self.log("   2. 或直接开始测试参数效果")

        else:
            # 获取滤镜失败，尝试列出所有滤镜
            self.log(f"❌ 未找到VST滤镜: {filter_name}")
            self.log("🔍 正在查找该音频源的所有滤镜...")

            filters_response = self.send_request("GetSourceFilterList", {
                "sourceName": source_name
            })

            if filters_response and filters_response.get('requestStatus', {}).get('result'):
                filters = filters_response.get('responseData', {}).get('filters', [])
                if filters:
                    self.log("📋 该音频源的所有滤镜:")
                    for f in filters:
                        filter_name_found = f.get('filterName', '')
                        filter_kind = f.get('filterKind', '')
                        self.log(f"   - {filter_name_found} ({filter_kind})")
                else:
                    self.log("❌ 该音频源没有任何滤镜")
            else:
                self.log("❌ 无法获取滤镜列表")

    def discover_parameters(self):
        """探测VST插件的实际参数"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("🔍 开始探测VST插件的实际参数...")
        self.log("=" * 50)

        # 获取当前滤镜设置
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.log("✅ 发现的实际参数:")
            self.log("-" * 40)

            self.discovered_params = {}
            for key, value in settings.items():
                if key != 'plugin_path':  # 跳过插件路径
                    try:
                        # 尝试转换为数字
                        numeric_value = float(value)
                        self.discovered_params[key] = {
                            "current": numeric_value,
                            "type": "numeric"
                        }
                        self.log(f"  📊 {key}: {numeric_value} (数值参数)")
                    except (ValueError, TypeError):
                        self.discovered_params[key] = {
                            "current": value,
                            "type": "other"
                        }
                        self.log(f"  📝 {key}: {value} (非数值参数)")

            # 更新参数下拉框
            if self.discovered_params:
                numeric_params = [k for k, v in self.discovered_params.items()
                                if v["type"] == "numeric"]
                if numeric_params:
                    self.param_combo['values'] = numeric_params
                    self.param_combo.set(numeric_params[0])
                    self.log(f"🎯 找到 {len(numeric_params)} 个可测试的数值参数")
                else:
                    self.log("⚠️ 未找到数值参数")

            self.log("-" * 40)
            self.log("💡 现在可以使用发现的参数进行测试了！")

        else:
            self.log("❌ 探测参数失败")

    def toggle_monitoring(self):
        """切换实时监控"""
        if self.monitor_var.get():
            self.start_monitoring()
        else:
            self.stop_monitoring()

    def start_monitoring(self):
        """开始实时监控"""
        if not self.monitoring_enabled:
            self.monitoring_enabled = True
            self.log("📡 开始实时监控参数变化...")

            # 存储上一次的参数状态
            self.last_params = {}

            def monitor_loop():
                while self.monitoring_enabled and self.is_connected:
                    try:
                        source_name = self.source_var.get()
                        filter_name = self.filter_var.get()

                        response = self.send_request("GetSourceFilter", {
                            "sourceName": source_name,
                            "filterName": filter_name
                        }, timeout=2)

                        if response and response.get('requestStatus', {}).get('result'):
                            settings = response.get('responseData', {}).get('filterSettings', {})

                            # 检查所有参数的变化
                            for key, value in settings.items():
                                if key == 'plugin_path':  # 跳过插件路径
                                    continue

                                # 如果是第一次监控，记录初始值
                                if key not in self.last_params:
                                    self.last_params[key] = value
                                    self.root.after(0, self.log,
                                        f"📋 初始参数: {key} = {value}")
                                    continue

                                old_value = self.last_params[key]

                                # 检查数值参数变化
                                try:
                                    old_num = float(old_value)
                                    new_num = float(value)
                                    # 使用更敏感的检测阈值
                                    if abs(new_num - old_num) > 0.0001:  # 极敏感检测
                                        diff = new_num - old_num
                                        self.root.after(0, self.log,
                                            f"📊 参数变化: {key} = {old_num:.6f} → {new_num:.6f} ({diff:+.6f})")
                                        self.last_params[key] = value

                                        # 标记可能的音调参数
                                        if self.is_likely_pitch_param(key, diff):
                                            self.root.after(0, self.log, f"   🎵 可能是音调参数！")

                                        # 同时更新discovered_params
                                        if key in self.discovered_params:
                                            self.discovered_params[key]["current"] = new_num
                                except (ValueError, TypeError):
                                    # 检查非数值参数变化
                                    if str(value) != str(old_value):
                                        self.root.after(0, self.log,
                                            f"📝 参数变化: {key} = {old_value} → {value}")
                                        self.last_params[key] = value

                                        # 同时更新discovered_params
                                        if key in self.discovered_params:
                                            self.discovered_params[key]["current"] = value

                        time.sleep(0.5)  # 每0.5秒检查一次，更快响应

                    except Exception as e:
                        self.root.after(0, self.log, f"❌ 监控错误: {e}")
                        time.sleep(2)  # 出错时等待2秒再继续

            self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
            self.monitor_thread.start()

    def stop_monitoring(self):
        """停止实时监控"""
        if self.monitoring_enabled:
            self.monitoring_enabled = False
            self.log("📡 停止实时监控")
            # 清理监控状态
            if hasattr(self, 'last_params'):
                param_count = len(self.last_params)
                self.log(f"📊 监控期间跟踪了 {param_count} 个参数")
                self.last_params = {}

    def is_likely_pitch_param(self, param_name, diff):
        """判断是否可能是音调参数"""
        param_lower = param_name.lower()

        # 检查参数名称
        pitch_keywords = ['pitch', 'tune', 'semitone', 'transpose', 'coarse', 'fine', 'freq', 'cents', 'detune', 'shift']
        if any(keyword in param_lower for keyword in pitch_keywords):
            return True

        # 检查数值变化范围（音调参数通常变化较明显）
        if abs(diff) > 0.01 and abs(diff) < 100:
            return True

        return False

    def manual_refresh(self):
        """手动刷新参数状态"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("🔄 手动刷新参数状态...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.log("📊 当前所有参数状态:")
            self.log("-" * 50)

            param_count = 0
            for key, value in settings.items():
                if key != 'plugin_path':  # 跳过插件路径
                    param_count += 1
                    try:
                        # 尝试显示为数值
                        num_value = float(value)
                        self.log(f"  📊 {key}: {num_value:.3f}")
                    except (ValueError, TypeError):
                        # 显示为字符串
                        self.log(f"  📝 {key}: {value}")

            self.log("-" * 50)
            self.log(f"✅ 共发现 {param_count} 个参数")

            # 如果正在监控，更新监控基准
            if self.monitoring_enabled and hasattr(self, 'last_params'):
                self.last_params.update(settings)
                self.log("📡 已更新监控基准值")

        else:
            self.log("❌ 刷新失败")

    def test_single_parameter(self):
        """测试单个参数"""
        param_name = self.param_combo.get()
        try:
            test_value = float(self.test_value_var.get())
        except ValueError:
            messagebox.showerror("错误", "测试值必须是数字")
            return

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 检查参数是否在发现的参数中
        if param_name in self.discovered_params:
            original_value = self.discovered_params[param_name]["current"]
            self.log(f"🎯 测试参数: {param_name}")
            self.log(f"   原始值: {original_value}")
            self.log(f"   测试值: {test_value}")
        else:
            self.log(f"🎯 测试参数: {param_name} = {test_value}")
            self.log("⚠️ 此参数未在探测中发现，可能无效")

        # 设置参数
        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: test_value
            }
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 参数设置请求成功")

            # 验证设置
            time.sleep(0.5)
            verify_response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if verify_response and verify_response.get('requestStatus', {}).get('result'):
                verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                if param_name in verify_settings:
                    actual_value = verify_settings[param_name]
                    self.log(f"📊 验证值: {actual_value}")

                    try:
                        if abs(float(actual_value) - test_value) < 0.01:
                            self.log("🎉 参数设置已确认生效！")
                            self.log("🎧 请检查OBS中的音频效果是否有变化")

                            # 更新发现的参数
                            if param_name in self.discovered_params:
                                self.discovered_params[param_name]["current"] = float(actual_value)
                        else:
                            self.log(f"⚠️ 设置值与验证值不匹配 (期望: {test_value}, 实际: {actual_value})")
                    except (ValueError, TypeError):
                        self.log(f"📝 参数值已更新为: {actual_value}")
                else:
                    self.log("❌ 验证时未找到参数")
            else:
                self.log("❌ 验证请求失败")
        else:
            self.log("❌ 参数设置失败")
            
    def reset_parameter(self):
        """重置参数到原始值"""
        param_name = self.param_combo.get()
        original_value = self.test_params[param_name]["current"]
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        self.log(f"🔄 重置参数: {param_name} = {original_value}")
        
        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: original_value
            }
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 参数已重置")
        else:
            self.log("❌ 参数重置失败")
            
    def test_all_parameters(self):
        """测试所有关键参数"""
        self.log("🚀 开始测试所有关键参数...")
        self.log("=" * 50)
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        # 测试每个参数的不同值
        for param_name, param_info in self.test_params.items():
            self.log(f"\n🧪 测试参数: {param_name}")
            self.log(f"当前值: {param_info['current']}")
            
            for test_value in param_info["test_values"]:
                self.log(f"  测试值: {test_value}")
                
                # 设置参数
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                }, timeout=2)
                
                if response and response.get('requestStatus', {}).get('result'):
                    self.log(f"    ✅ 设置成功")
                    time.sleep(1)  # 等待效果生效
                else:
                    self.log(f"    ❌ 设置失败")
                    
            # 恢复原始值
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: param_info["current"]
                }
            }, timeout=2)
            
        self.log("\n🎉 所有参数测试完成！")
        self.log("🎧 请观察测试过程中音频效果的变化")
        
    def demo_effects(self):
        """演示效果"""
        self.log("🎭 开始演示Graillon效果...")
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        demo_sequence = [
            ("pitch_shift", 0.0, "正常音调"),
            ("pitch_shift", 5.0, "升高5半音"),
            ("pitch_shift", -5.0, "降低5半音"),
            ("pitch_shift", 0.0, "恢复正常"),
            ("formant_shift", 1.5, "提高共振峰"),
            ("formant_shift", 0.8, "降低共振峰"),
            ("formant_shift", 1.1, "恢复共振峰"),
            ("correction_strength", 1.0, "最大自动校正"),
            ("correction_strength", 0.0, "关闭自动校正"),
            ("correction_strength", 0.8, "恢复校正强度"),
        ]
        
        for param_name, value, description in demo_sequence:
            self.log(f"🎵 {description}: {param_name} = {value}")
            
            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: value
                }
            }, timeout=2)
            
            if response and response.get('requestStatus', {}).get('result'):
                self.log("  ✅ 效果已应用")
                time.sleep(2)  # 等待2秒让用户听到效果
            else:
                self.log("  ❌ 效果应用失败")
                
        self.log("🎭 演示完成！")

    def test_extreme_values(self):
        """测试极端值，更容易观察到变化"""
        self.log("⚡ 开始极端值测试...")
        self.log("这个测试使用极端数值，应该能看到明显变化")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 极端值测试序列
        extreme_tests = [
            # 音调测试 - 应该最明显
            ("pitch_shift", -12.0, "音调降低一个八度"),
            ("pitch_shift", 0.0, "恢复正常音调"),
            ("pitch_shift", 12.0, "音调升高一个八度"),
            ("pitch_shift", 0.0, "恢复正常音调"),

            # 移调测试
            ("transpose", -12, "移调降低一个八度"),
            ("transpose", 0, "恢复正常移调"),
            ("transpose", 12, "移调升高一个八度"),
            ("transpose", 2, "恢复原始移调"),

            # 粗调测试
            ("coarse", -12, "粗调最低"),
            ("coarse", 0, "粗调中性"),
            ("coarse", 12, "粗调最高"),
            ("coarse", 2, "恢复原始粗调"),

            # 微调测试
            ("fine", -100, "微调最低"),
            ("fine", 0, "微调中性"),
            ("fine", 100, "微调最高"),
            ("fine", 10, "恢复原始微调"),

            # 共振峰测试
            ("formant_shift", 0.5, "共振峰最低"),
            ("formant_shift", 2.0, "共振峰最高"),
            ("formant_shift", 1.1, "恢复原始共振峰"),

            # 校正强度测试
            ("correction_strength", 0.0, "关闭自动校正"),
            ("correction_strength", 1.0, "最大自动校正"),
            ("correction_strength", 0.8, "恢复原始校正强度"),
        ]

        for param_name, test_value, description in extreme_tests:
            self.log(f"\n🧪 {description}")
            self.log(f"   设置: {param_name} = {test_value}")

            # 设置参数
            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: test_value
                }
            }, timeout=3)

            if response and response.get('requestStatus', {}).get('result'):
                self.log("   ✅ 设置成功")

                # 验证设置
                time.sleep(0.5)
                verify_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                }, timeout=2)

                if verify_response and verify_response.get('requestStatus', {}).get('result'):
                    verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                    if param_name in verify_settings:
                        actual_value = verify_settings[param_name]
                        self.log(f"   📊 实际值: {actual_value}")

                        # 检查值是否真的改变了
                        if str(actual_value) == str(test_value):
                            self.log("   🎯 参数值已确认改变！")
                        else:
                            self.log(f"   ⚠️ 参数值不匹配 (期望: {test_value}, 实际: {actual_value})")
                    else:
                        self.log("   ❌ 验证时未找到参数")

                # 等待3秒让用户听到效果
                self.log("   🎧 请仔细听音频变化...")
                time.sleep(3)

            else:
                self.log("   ❌ 设置失败")

        self.log("\n⚡ 极端值测试完成！")
        self.log("🎧 如果听到了音调或音色的明显变化，说明参数控制有效")
        self.log("=" * 50)

    def restore_defaults(self):
        """恢复默认值"""
        self.log("🏠 恢复所有参数到默认值...")
        
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        default_settings = {}
        for param_name, param_info in self.test_params.items():
            default_settings[param_name] = param_info["current"]
            
        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": default_settings
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 所有参数已恢复到默认值")
        else:
            self.log("❌ 恢复默认值失败")

    def smart_test_discovered(self):
        """智能测试发现的参数"""
        if not self.discovered_params:
            self.log("⚠️ 请先点击'探测参数'发现实际参数")
            return

        self.log("🧠 开始智能测试发现的参数...")
        self.log("=" * 50)

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        # 只测试数值参数
        numeric_params = {k: v for k, v in self.discovered_params.items()
                         if v["type"] == "numeric"}

        if not numeric_params:
            self.log("❌ 未发现可测试的数值参数")
            return

        self.log(f"🎯 发现 {len(numeric_params)} 个数值参数，开始智能测试...")

        for param_name, param_info in numeric_params.items():
            current_value = param_info["current"]
            self.log(f"\n🧪 测试参数: {param_name}")
            self.log(f"   当前值: {current_value}")

            # 根据当前值智能生成测试值
            test_values = self.generate_smart_test_values(current_value)
            self.log(f"   测试值: {test_values}")

            for test_value in test_values:
                self.log(f"     → 测试: {test_value}")

                # 设置参数
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {
                        param_name: test_value
                    }
                }, timeout=3)

                if response and response.get('requestStatus', {}).get('result'):
                    # 验证设置
                    time.sleep(0.5)
                    verify_response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=2)

                    if verify_response and verify_response.get('requestStatus', {}).get('result'):
                        verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                        if param_name in verify_settings:
                            actual_value = verify_settings[param_name]
                            try:
                                if abs(float(actual_value) - test_value) < 0.01:
                                    self.log(f"       ✅ 成功设置为: {actual_value}")
                                else:
                                    self.log(f"       ⚠️ 设置为: {actual_value} (期望: {test_value})")
                            except (ValueError, TypeError):
                                self.log(f"       📝 设置为: {actual_value}")
                        else:
                            self.log(f"       ❌ 验证失败")

                    # 等待观察效果
                    time.sleep(1.5)
                else:
                    self.log(f"       ❌ 设置失败")

            # 恢复原始值
            self.log(f"   🔄 恢复原始值: {current_value}")
            self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    param_name: current_value
                }
            }, timeout=2)
            time.sleep(0.5)

        self.log("\n🧠 智能测试完成！")
        self.log("🎧 请观察测试过程中音频效果的变化")
        self.log("=" * 50)

    def generate_smart_test_values(self, current_value):
        """根据当前值智能生成测试值"""
        try:
            current = float(current_value)

            # 根据数值范围智能生成测试值
            if -20 <= current <= 20:  # 可能是音调参数 (-12到12半音)
                return [-12.0, -6.0, 0.0, 6.0, 12.0]
            elif 0 <= current <= 200:  # 可能是百分比参数 (0-100%)
                return [0.0, 25.0, 50.0, 100.0, 150.0]
            elif 0 <= current <= 2:  # 可能是比例参数 (0-1或0-2)
                return [0.0, 0.5, 1.0, 1.5, 2.0]
            else:  # 其他范围，基于当前值生成
                base = abs(current) if current != 0 else 1
                return [
                    current - base,
                    current - base/2,
                    current,
                    current + base/2,
                    current + base
                ]
        except (ValueError, TypeError):
            # 如果无法转换为数字，返回默认测试值
            return [0.0, 0.5, 1.0]

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTParameterTester()
    app.run()
