#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 VST插件ID检测器启动脚本
快速启动VST插件ID检测工具
"""

import sys
import os

def main():
    """主函数"""
    print("🎛️ 启动VST插件ID检测器...")
    print("="*50)
    
    try:
        # 导入并运行VST检测器
        from vst_plugin_id_detector import VST_Plugin_ID_Detector
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app.setApplicationName("VST插件ID检测器")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("OBS工具集")
        
        # 创建并显示主窗口
        window = VST_Plugin_ID_Detector()
        window.show()
        
        print("✅ VST插件ID检测器已启动")
        print("📋 功能说明:")
        print("   • 连接OBS后可检测VST2x插件")
        print("   • 获取插件ID和参数信息")
        print("   • 实时监控参数变化")
        print("   • 导出检测结果为JSON文件")
        print("="*50)
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装PyQt5:")
        print("pip install PyQt5")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
