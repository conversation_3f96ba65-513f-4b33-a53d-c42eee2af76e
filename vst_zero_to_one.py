#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔬 VST参数从零开始探索器
完全不依赖任何假设，从OBS API直接获取并测试所有真实参数
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import json
import websocket
import threading
import time
from datetime import datetime

class VSTZeroToOne:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔬 VST参数从零开始探索器")
        self.root.geometry("1400x900")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 发现的数据
        self.discovered_sources = []
        self.discovered_filters = []
        self.discovered_params = {}
        self.baseline_params = {}  # 基线参数值

        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 探索阶段
        explore_frame = ttk.LabelFrame(main_frame, text="🔍 第一阶段：探索发现", padding="10")
        explore_frame.pack(fill=tk.X, pady=(0, 10))
        
        explore_row1 = ttk.Frame(explore_frame)
        explore_row1.pack(fill=tk.X, pady=(0, 5))
        
        self.step1_btn = ttk.Button(explore_row1, text="1️⃣ 发现所有音频源", 
                                   command=self.step1_discover_sources, state="disabled")
        self.step1_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.step2_btn = ttk.Button(explore_row1, text="2️⃣ 发现所有VST滤镜", 
                                   command=self.step2_discover_filters, state="disabled")
        self.step2_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.step3_btn = ttk.Button(explore_row1, text="3️⃣ 获取所有参数", 
                                   command=self.step3_get_all_params, state="disabled")
        self.step3_btn.pack(side=tk.LEFT)
        
        # 选择目标
        target_frame = ttk.LabelFrame(main_frame, text="🎯 第二阶段：选择目标", padding="10")
        target_frame.pack(fill=tk.X, pady=(0, 10))
        
        target_row = ttk.Frame(target_frame)
        target_row.pack(fill=tk.X)
        
        ttk.Label(target_row, text="音频源:").pack(side=tk.LEFT)
        self.source_combo = ttk.Combobox(target_row, state="readonly", width=20)
        self.source_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(target_row, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_combo = ttk.Combobox(target_row, state="readonly", width=20)
        self.filter_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        self.select_target_btn = ttk.Button(target_row, text="✅ 确认目标", 
                                           command=self.select_target, state="disabled")
        self.select_target_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # 测试阶段
        test_frame = ttk.LabelFrame(main_frame, text="🧪 第三阶段：系统测试", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        test_row1 = ttk.Frame(test_frame)
        test_row1.pack(fill=tk.X, pady=(0, 5))
        
        self.enable_filter_btn = ttk.Button(test_row1, text="🔛 启用VST滤镜", 
                                           command=self.enable_target_filter, state="disabled")
        self.enable_filter_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.baseline_btn = ttk.Button(test_row1, text="📊 建立基线", 
                                      command=self.establish_baseline, state="disabled")
        self.baseline_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_all_btn = ttk.Button(test_row1, text="🚀 测试所有参数", 
                                      command=self.test_all_parameters, state="disabled")
        self.test_all_btn.pack(side=tk.LEFT)
        
        test_row2 = ttk.Frame(test_frame)
        test_row2.pack(fill=tk.X)
        
        ttk.Label(test_row2, text="单独测试:").pack(side=tk.LEFT)
        self.param_combo = ttk.Combobox(test_row2, state="readonly", width=20)
        self.param_combo.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(test_row2, text="值:").pack(side=tk.LEFT)
        self.test_value_var = tk.StringVar(value="0")
        ttk.Entry(test_row2, textvariable=self.test_value_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        self.test_single_btn = ttk.Button(test_row2, text="🎯 测试单个", 
                                         command=self.test_single_param, state="disabled")
        self.test_single_btn.pack(side=tk.LEFT, padx=(10, 5))
        
        self.restore_btn = ttk.Button(test_row2, text="🔄 恢复基线", 
                                     command=self.restore_baseline, state="disabled")
        self.restore_btn.pack(side=tk.LEFT)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="📋 探索日志", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🔬 VST参数从零开始探索器")
        self.log("完全不依赖任何假设，系统性地发现和测试VST参数")
        self.log("=" * 70)
        self.log("📋 探索流程:")
        self.log("  第一阶段：发现所有音频源和VST滤镜")
        self.log("  第二阶段：选择目标进行深度分析")
        self.log("  第三阶段：系统性测试所有参数")
        self.log("=" * 70)
        self.log("🚀 请先点击'连接OBS'开始探索")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # WebSocket握手
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用第一阶段按钮
        self.step1_btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🎉 现在可以开始探索了！")
        self.log("💡 请按顺序点击 1️⃣ → 2️⃣ → 3️⃣ 完成发现阶段")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.step1_btn, self.step2_btn, self.step3_btn, self.select_target_btn,
                  self.enable_filter_btn, self.baseline_btn, self.test_all_btn, 
                  self.test_single_btn, self.restore_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None

    def step1_discover_sources(self):
        """第一步：发现所有音频源"""
        self.log("1️⃣ 开始发现所有音频源...")
        self.log("=" * 50)

        response = self.send_request("GetInputList")

        if response and response.get('requestStatus', {}).get('result'):
            inputs = response.get('responseData', {}).get('inputs', [])

            self.discovered_sources = []
            self.log(f"✅ 发现 {len(inputs)} 个输入源:")

            for inp in inputs:
                input_name = inp.get('inputName', '')
                input_kind = inp.get('inputKind', '')

                self.discovered_sources.append({
                    'name': input_name,
                    'kind': input_kind
                })

                # 判断是否可能包含音频
                audio_likely = any(keyword in input_kind.lower()
                                 for keyword in ['audio', 'media', 'capture', 'source'])
                icon = "🎵" if audio_likely else "📺"

                self.log(f"  {icon} {input_name} ({input_kind})")

            # 更新下拉框
            source_names = [s['name'] for s in self.discovered_sources]
            self.source_combo['values'] = source_names
            if source_names:
                self.source_combo.set(source_names[0])

            self.log("=" * 50)
            self.log("✅ 第一步完成！现在点击 2️⃣ 发现VST滤镜")

            # 启用下一步
            self.step2_btn.config(state="normal")

        else:
            self.log("❌ 获取输入源失败")

    def step2_discover_filters(self):
        """第二步：发现所有VST滤镜"""
        self.log("2️⃣ 开始发现所有VST滤镜...")
        self.log("=" * 50)

        self.discovered_filters = []

        for source in self.discovered_sources:
            source_name = source['name']
            self.log(f"🔍 检查源: {source_name}")

            response = self.send_request("GetSourceFilterList", {
                "sourceName": source_name
            })

            if response and response.get('requestStatus', {}).get('result'):
                filters = response.get('responseData', {}).get('filters', [])

                if filters:
                    for f in filters:
                        filter_name = f.get('filterName', '')
                        filter_kind = f.get('filterKind', '')
                        filter_enabled = f.get('filterEnabled', False)

                        # 检查是否是VST滤镜
                        if 'vst' in filter_kind.lower():
                            self.discovered_filters.append({
                                'source_name': source_name,
                                'filter_name': filter_name,
                                'filter_kind': filter_kind,
                                'enabled': filter_enabled
                            })

                            status = "✅" if filter_enabled else "❌"
                            self.log(f"  🎛️ VST滤镜: {filter_name} {status}")
                        else:
                            status = "✅" if filter_enabled else "❌"
                            self.log(f"  📊 其他滤镜: {filter_name} ({filter_kind}) {status}")
                else:
                    self.log("  📭 无滤镜")
            else:
                self.log(f"  ❌ 获取滤镜失败")

        self.log("=" * 50)
        if self.discovered_filters:
            self.log(f"✅ 发现 {len(self.discovered_filters)} 个VST滤镜:")
            for vst in self.discovered_filters:
                self.log(f"  📍 {vst['source_name']} -> {vst['filter_name']}")

            # 更新下拉框
            filter_options = [f"{vst['source_name']} -> {vst['filter_name']}"
                            for vst in self.discovered_filters]
            self.filter_combo['values'] = filter_options
            if filter_options:
                self.filter_combo.set(filter_options[0])

            self.log("✅ 第二步完成！现在点击 3️⃣ 获取所有参数")
            self.step3_btn.config(state="normal")
        else:
            self.log("❌ 未发现任何VST滤镜")

    def step3_get_all_params(self):
        """第三步：获取所有参数"""
        self.log("3️⃣ 开始获取所有VST参数...")
        self.log("=" * 50)

        self.discovered_params = {}

        for vst in self.discovered_filters:
            source_name = vst['source_name']
            filter_name = vst['filter_name']

            self.log(f"🔍 分析: {source_name} -> {filter_name}")

            response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if response and response.get('requestStatus', {}).get('result'):
                filter_data = response.get('responseData', {})
                settings = filter_data.get('filterSettings', {})

                vst_key = f"{source_name} -> {filter_name}"
                self.discovered_params[vst_key] = {}

                self.log(f"  📊 发现 {len(settings)} 个参数:")

                param_count = 0
                for key, value in settings.items():
                    param_count += 1

                    # 分析参数类型和可能用途
                    if key == 'plugin_path':
                        self.log(f"    📁 {param_count:2d}. {key}: {value}")
                    elif key in ['chunk_data', 'chunk_hash']:
                        self.log(f"    💾 {param_count:2d}. {key}: [二进制数据]")
                    else:
                        # 这些是真正的VST参数
                        try:
                            num_value = float(value)
                            self.discovered_params[vst_key][key] = {
                                'value': num_value,
                                'type': 'numeric',
                                'original': num_value
                            }
                            self.log(f"    🎚️ {param_count:2d}. {key}: {num_value}")
                        except (ValueError, TypeError):
                            self.discovered_params[vst_key][key] = {
                                'value': value,
                                'type': 'other',
                                'original': value
                            }
                            self.log(f"    📝 {param_count:2d}. {key}: {value}")

                # 检查插件类型
                plugin_path = settings.get('plugin_path', '')
                if plugin_path:
                    import os
                    plugin_name = os.path.basename(plugin_path).lower()
                    if 'graillon' in plugin_name:
                        self.log(f"    🎉 确认这是Graillon插件！")
                    else:
                        self.log(f"    ℹ️ 插件: {os.path.basename(plugin_path)}")

            else:
                self.log(f"  ❌ 获取参数失败")

        self.log("=" * 50)
        if self.discovered_params:
            total_params = sum(len(params) for params in self.discovered_params.values())
            self.log(f"✅ 第三步完成！总共发现 {total_params} 个参数")
            self.log("🎯 现在可以选择目标进行测试")

            # 启用目标选择
            self.select_target_btn.config(state="normal")
        else:
            self.log("❌ 未发现任何参数")

    def select_target(self):
        """选择测试目标"""
        selected_filter = self.filter_combo.get()
        if not selected_filter:
            self.log("❌ 请先选择VST滤镜")
            return

        self.log(f"🎯 选择测试目标: {selected_filter}")

        # 解析选择的滤镜
        if " -> " in selected_filter:
            self.target_source, self.target_filter = selected_filter.split(" -> ", 1)
        else:
            self.log("❌ 滤镜格式错误")
            return

        # 获取该滤镜的参数
        if selected_filter in self.discovered_params:
            params = self.discovered_params[selected_filter]
            numeric_params = [key for key, info in params.items()
                            if info['type'] == 'numeric']

            if numeric_params:
                self.param_combo['values'] = numeric_params
                self.param_combo.set(numeric_params[0])

                self.log(f"✅ 目标已确认，发现 {len(numeric_params)} 个可测试参数:")
                for param in numeric_params:
                    value = params[param]['value']
                    self.log(f"  🎚️ {param}: {value}")

                # 启用测试阶段按钮
                buttons = [self.enable_filter_btn, self.baseline_btn, self.test_all_btn,
                          self.test_single_btn, self.restore_btn]
                for btn in buttons:
                    btn.config(state="normal")

                self.log("🚀 现在可以开始测试了！建议顺序:")
                self.log("  1. 🔛 启用VST滤镜")
                self.log("  2. 📊 建立基线")
                self.log("  3. 🚀 测试所有参数")
            else:
                self.log("❌ 该滤镜没有可测试的数值参数")
        else:
            self.log("❌ 未找到该滤镜的参数数据")

    def enable_target_filter(self):
        """启用目标VST滤镜"""
        self.log(f"🔛 启用VST滤镜: {self.target_source} -> {self.target_filter}")

        response = self.send_request("SetSourceFilterEnabled", {
            "sourceName": self.target_source,
            "filterName": self.target_filter,
            "filterEnabled": True
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ VST滤镜已启用")

            # 验证启用状态
            time.sleep(0.5)
            verify_response = self.send_request("GetSourceFilter", {
                "sourceName": self.target_source,
                "filterName": self.target_filter
            })

            if verify_response and verify_response.get('requestStatus', {}).get('result'):
                enabled = verify_response.get('responseData', {}).get('filterEnabled', False)
                if enabled:
                    self.log("✅ 确认滤镜已启用，现在应该可以听到音频效果")
                else:
                    self.log("⚠️ 滤镜可能未正确启用")
        else:
            self.log("❌ 启用VST滤镜失败")

    def establish_baseline(self):
        """建立参数基线"""
        self.log("📊 建立参数基线...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": self.target_source,
            "filterName": self.target_filter
        })

        if response and response.get('requestStatus', {}).get('result'):
            settings = response.get('responseData', {}).get('filterSettings', {})

            self.baseline_params = {}
            param_count = 0

            self.log("📊 当前参数基线值:")
            for key, value in settings.items():
                if key not in ['plugin_path', 'chunk_data', 'chunk_hash']:
                    try:
                        num_value = float(value)
                        self.baseline_params[key] = num_value
                        param_count += 1
                        self.log(f"  🎚️ {key}: {num_value}")
                    except (ValueError, TypeError):
                        pass

            self.log(f"✅ 基线已建立，记录了 {param_count} 个参数")
            self.log("💡 现在可以开始测试参数变化了")
        else:
            self.log("❌ 建立基线失败")

    def test_all_parameters(self):
        """测试所有参数"""
        if not self.baseline_params:
            self.log("❌ 请先建立基线")
            return

        self.log("🚀 开始系统测试所有参数...")
        self.log("=" * 60)

        # 测试策略：对每个参数测试多个值
        test_values = [-10, -5, -1, 0, 1, 5, 10, 20, 50, 100]

        for param_name, baseline_value in self.baseline_params.items():
            self.log(f"\n🧪 测试参数: {param_name}")
            self.log(f"   基线值: {baseline_value}")

            effective_values = []

            for test_value in test_values:
                # 跳过与基线相同的值
                if abs(test_value - baseline_value) < 0.01:
                    continue

                self.log(f"     测试值: {test_value}")

                # 设置参数
                response = self.send_request("SetSourceFilterSettings", {
                    "sourceName": self.target_source,
                    "filterName": self.target_filter,
                    "filterSettings": {
                        param_name: test_value
                    }
                }, timeout=3)

                if response and response.get('requestStatus', {}).get('result'):
                    # 验证设置
                    time.sleep(0.3)
                    verify_response = self.send_request("GetSourceFilter", {
                        "sourceName": self.target_source,
                        "filterName": self.target_filter
                    }, timeout=2)

                    if verify_response and verify_response.get('requestStatus', {}).get('result'):
                        verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                        if param_name in verify_settings:
                            actual_value = verify_settings[param_name]
                            try:
                                if abs(float(actual_value) - test_value) < 0.01:
                                    self.log(f"       ✅ 成功: {actual_value}")
                                    effective_values.append(test_value)
                                    time.sleep(1)  # 给用户时间听效果
                                else:
                                    self.log(f"       ⚠️ 不匹配: {actual_value}")
                            except (ValueError, TypeError):
                                self.log(f"       📝 设置为: {actual_value}")
                        else:
                            self.log(f"       ❌ 验证失败")
                    else:
                        self.log(f"       ❌ 验证请求失败")
                else:
                    self.log(f"       ❌ 设置失败")

            # 恢复基线值
            self.log(f"   🔄 恢复基线值: {baseline_value}")
            self.send_request("SetSourceFilterSettings", {
                "sourceName": self.target_source,
                "filterName": self.target_filter,
                "filterSettings": {
                    param_name: baseline_value
                }
            }, timeout=2)

            # 总结该参数的测试结果
            if effective_values:
                self.log(f"   📊 有效值: {effective_values}")
                self.log(f"   🎯 该参数可能有效！")
            else:
                self.log(f"   📭 未发现有效变化")

            time.sleep(0.5)

        self.log("\n🚀 所有参数测试完成！")
        self.log("🎧 如果在测试过程中听到了音频变化，")
        self.log("   说明对应的参数是有效的音频控制参数")
        self.log("=" * 60)

    def test_single_param(self):
        """测试单个参数"""
        param_name = self.param_combo.get()
        if not param_name:
            self.log("❌ 请选择要测试的参数")
            return

        try:
            test_value = float(self.test_value_var.get())
        except ValueError:
            self.log("❌ 测试值必须是数字")
            return

        baseline_value = self.baseline_params.get(param_name, 0)

        self.log(f"🎯 单独测试参数: {param_name}")
        self.log(f"   基线值: {baseline_value}")
        self.log(f"   测试值: {test_value}")

        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": self.target_source,
            "filterName": self.target_filter,
            "filterSettings": {
                param_name: test_value
            }
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 参数设置成功")
            self.log("🎧 请仔细听音频变化...")

            # 验证设置
            time.sleep(0.5)
            verify_response = self.send_request("GetSourceFilter", {
                "sourceName": self.target_source,
                "filterName": self.target_filter
            })

            if verify_response and verify_response.get('requestStatus', {}).get('result'):
                verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                if param_name in verify_settings:
                    actual_value = verify_settings[param_name]
                    self.log(f"📊 实际值: {actual_value}")

                    if abs(float(actual_value) - test_value) < 0.01:
                        self.log("🎉 参数设置已确认！")
                        if abs(test_value - baseline_value) > 1:
                            self.log("💡 如果听到了变化，说明这个参数有效")
                    else:
                        self.log(f"⚠️ 值不匹配 (期望: {test_value}, 实际: {actual_value})")
        else:
            self.log("❌ 参数设置失败")

    def restore_baseline(self):
        """恢复所有参数到基线值"""
        if not self.baseline_params:
            self.log("❌ 没有基线数据")
            return

        self.log("🔄 恢复所有参数到基线值...")

        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": self.target_source,
            "filterName": self.target_filter,
            "filterSettings": self.baseline_params
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 所有参数已恢复到基线值")
            for param, value in self.baseline_params.items():
                self.log(f"   {param}: {value}")
        else:
            self.log("❌ 恢复基线失败")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTZeroToOne()
    app.run()
