import obsws_python as obs
import time
import sys

# ===== 配置区域 =====
OBS_HOST = "localhost"        # OBS运行的主机
OBS_PORT = 4455               # WebSocket端口（默认4455）
OBS_PASSWORD = None           # 无密码
AUDIO_SOURCE = "媒体源"       # 媒体源名称
VST_FILTER = "VST 2.x 插件"   # VST滤镜名称
PARAM_NAME = "pitch"          # 要控制的参数名称（部分匹配即可）
# ====================

def connect_obs():
    """连接到OBS WebSocket"""
    try:
        return obs.ReqClient(
            host=OBS_HOST,
            port=OBS_PORT,
            password=OBS_PASSWORD,
            timeout=3
        )
    except Exception as e:
        print(f"连接OBS失败: {str(e)}")
        print("请检查: 1. OBS是否运行 2. WebSocket插件是否安装 3. 端口是否正确")
        sys.exit(1)

def find_vst_param(client, partial_name):
    """查找匹配的参数名"""
    try:
        # 获取滤镜当前设置
        settings = client.get_source_filter_settings(
            source_name=AUDIO_SOURCE,
            filter_name=VST_FILTER
        )
        
        # 搜索匹配参数
        param_name = None
        all_params = []
        
        print("\n===== 滤镜参数列表 =====")
        for param, value in settings.filter_settings.items():
            print(f"{param}: {value}")
            all_params.append(param)
            
            # 不区分大小写匹配
            if partial_name.lower() in param.lower():
                param_name = param
        
        if not param_name and partial_name:
            print(f"\n警告: 找不到包含 '{partial_name}' 的参数")
            print("可用的参数列表:")
            for p in all_params:
                print(f"  - {p}")
            
            # 让用户手动选择参数
            if all_params:
                print("\n请从以上列表中选择一个参数名:")
                param_name = input("输入完整的参数名: ")
        
        if not param_name:
            print("\n错误: 没有找到可用参数")
            return None
        
        # 获取参数当前值
        current_value = settings.filter_settings[param_name]
        
        print(f"\n找到目标参数: {param_name}")
        print(f"当前值: {current_value}")
        
        return param_name, current_value
    
    except Exception as e:
        print(f"获取滤镜设置失败: {str(e)}")
        print(f"可能原因: 1. 媒体源 '{AUDIO_SOURCE}' 不存在 2. 滤镜 '{VST_FILTER}' 不存在 3. OBS版本不兼容")
        return None

def test_parameter_control(client, param_name, original_value):
    """测试参数控制"""
    print("\n===== 开始参数控制测试 =====")
    
    # 测试值范围（根据原始值动态调整）
    test_values = [
        original_value,
        original_value + 3.0,
        original_value + 6.0,
        original_value + 9.0,
        original_value + 6.0,
        original_value + 3.0,
        original_value
    ]
    
    # 确保值在合理范围内
    test_values = [max(min(v, 24.0), -24.0) for v in test_values]  # 限制在±24半音内
    
    for value in test_values:
        try:
            print(f"设置 {param_name} = {value}")
            
            # 修改参数
            client.set_source_filter_settings(
                source_name=AUDIO_SOURCE,
                filter_name=VST_FILTER,
                filter_settings={param_name: float(value)}
            )
            
            # 等待效果生效
            time.sleep(1.5)
            
        except Exception as e:
            print(f"设置参数失败: {str(e)}")
            break
    
    # 恢复原始值
    try:
        client.set_source_filter_settings(
            source_name=AUDIO_SOURCE,
            filter_name=VST_FILTER,
            filter_settings={param_name: float(original_value)}
        )
        print("\n测试完成，参数已恢复原始值")
    except:
        print("\n恢复原始值失败，请手动检查")

def main():
    print("===== OBS VST2X滤镜控制测试 =====")
    print(f"目标: 控制 '{AUDIO_SOURCE}' 的 '{VST_FILTER}' 滤镜")
    
    # 连接OBS
    client = connect_obs()
    print("成功连接到OBS")
    
    # 查找目标参数
    result = find_vst_param(client, PARAM_NAME)
    if not result:
        print("\n测试终止，请检查配置后重试")
        return
    
    param_name, original_value = result
    
    # 执行控制测试
    test_parameter_control(client, param_name, original_value)
    
    # 关闭连接
    print("\n测试结束，请检查OBS中的音频效果变化")

if __name__ == "__main__":
    main()