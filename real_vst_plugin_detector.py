#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ 真实VST插件ID检测器
连接到OBS WebSocket，获取真实的VST2x插件ID和参数信息
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import time
import websocket
import threading
from datetime import datetime

class RealVSTPluginDetector:
    """真实VST插件ID检测器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ 真实VST插件ID检测器 - 连接OBS获取VST2x插件信息")
        self.root.geometry("1400x900")
        
        # OBS连接相关
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        self.pending_requests = {}
        
        # 检测到的数据
        self.sources_data = {}
        self.current_source_filters = {}
        self.current_plugin_data = {}
        
        # 设置样式
        self.setup_styles()
        
        # 初始化UI
        self.setup_ui()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green', font=('Arial', 10, 'bold'))
        style.configure('Error.TLabel', foreground='red', font=('Arial', 10, 'bold'))
        style.configure('Warning.TLabel', foreground='orange', font=('Arial', 10, 'bold'))
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 顶部控制区域
        self.setup_control_area(main_frame)
        
        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧面板
        self.setup_left_panel(content_frame)
        
        # 右侧面板
        self.setup_right_panel(content_frame)
        
        # 底部状态栏
        self.setup_status_bar(main_frame)
        
    def setup_control_area(self, parent):
        """设置顶部控制区域"""
        control_frame = ttk.LabelFrame(parent, text="🔧 OBS连接控制", padding="10")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(5, weight=1)
        
        # OBS连接状态
        self.connection_label = ttk.Label(control_frame, text="❌ 未连接到OBS", style='Error.TLabel')
        self.connection_label.grid(row=0, column=0, padx=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="🔗 连接OBS", command=self.toggle_obs_connection)
        self.connect_btn.grid(row=0, column=1, padx=(0, 20))
        
        # WebSocket地址
        ttk.Label(control_frame, text="WebSocket地址:").grid(row=0, column=2, padx=(0, 5))
        self.ws_url_var = tk.StringVar(value="ws://localhost:4455")
        ws_url_entry = ttk.Entry(control_frame, textvariable=self.ws_url_var, width=20)
        ws_url_entry.grid(row=0, column=3, padx=(0, 10))
        
        # 刷新源列表按钮
        self.refresh_sources_btn = ttk.Button(control_frame, text="🔄 刷新源列表", 
                                            command=self.refresh_sources, state='disabled')
        self.refresh_sources_btn.grid(row=0, column=6, padx=(10, 0))
        
    def setup_left_panel(self, parent):
        """设置左侧面板"""
        left_frame = ttk.Frame(parent, width=450)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)
        
        # 音频源选择
        source_frame = ttk.LabelFrame(left_frame, text="🎤 音频源选择", padding="10")
        source_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        source_frame.columnconfigure(1, weight=1)
        
        ttk.Label(source_frame, text="选择音频源:").grid(row=0, column=0, padx=(0, 10))
        self.source_combo = ttk.Combobox(source_frame, state="readonly", width=25)
        self.source_combo.grid(row=0, column=1, sticky=(tk.W, tk.E))
        self.source_combo.bind('<<ComboboxSelected>>', self.on_source_selected)
        
        # 检测到的VST滤镜
        filters_frame = ttk.LabelFrame(left_frame, text="🎛️ 检测到的VST滤镜", padding="10")
        filters_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        filters_frame.columnconfigure(0, weight=1)
        filters_frame.rowconfigure(0, weight=1)
        
        # 滤镜列表
        columns = ('name', 'kind', 'enabled')
        self.filters_tree = ttk.Treeview(filters_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        self.filters_tree.heading('name', text='滤镜名称')
        self.filters_tree.heading('kind', text='滤镜类型')
        self.filters_tree.heading('enabled', text='启用状态')
        
        # 设置列宽
        self.filters_tree.column('name', width=150)
        self.filters_tree.column('kind', width=120)
        self.filters_tree.column('enabled', width=80)
        
        self.filters_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.filters_tree.bind('<<TreeviewSelect>>', self.on_filter_selected)
        
        # 滚动条
        filters_scrollbar = ttk.Scrollbar(filters_frame, orient=tk.VERTICAL, command=self.filters_tree.yview)
        filters_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.filters_tree.configure(yscrollcommand=filters_scrollbar.set)
        
        # 操作按钮
        btn_frame = ttk.Frame(filters_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        btn_frame.columnconfigure(0, weight=1)
        btn_frame.columnconfigure(1, weight=1)
        btn_frame.columnconfigure(2, weight=1)
        
        self.detect_filters_btn = ttk.Button(btn_frame, text="🔍 检测滤镜", 
                                           command=self.detect_filters, state='disabled')
        self.detect_filters_btn.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        
        self.analyze_vst_btn = ttk.Button(btn_frame, text="📊 分析VST", 
                                        command=self.analyze_vst_filter, state='disabled')
        self.analyze_vst_btn.grid(row=0, column=1, padx=(2, 3), sticky=(tk.W, tk.E))
        
        self.export_btn = ttk.Button(btn_frame, text="💾 导出", 
                                   command=self.export_results, state='disabled')
        self.export_btn.grid(row=0, column=2, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 快速操作
        quick_frame = ttk.LabelFrame(left_frame, text="⚡ 快速操作", padding="10")
        quick_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        quick_frame.columnconfigure(0, weight=1)
        
        self.auto_detect_btn = ttk.Button(quick_frame, text="🚀 一键检测所有VST插件", 
                                        command=self.auto_detect_all_vst, state='disabled')
        self.auto_detect_btn.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
    def setup_right_panel(self, parent):
        """设置右侧面板"""
        right_frame = ttk.Frame(parent)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=1)
        
        # 使用Notebook创建标签页
        self.notebook = ttk.Notebook(right_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # VST插件信息标签页
        self.setup_vst_info_tab()
        
        # 参数列表标签页
        self.setup_parameters_tab()
        
        # 原始数据标签页
        self.setup_raw_data_tab()
        
        # 日志标签页
        self.setup_log_tab()
        
    def setup_vst_info_tab(self):
        """设置VST插件信息标签页"""
        info_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(info_frame, text="🔍 VST插件信息")
        
        # VST基本信息
        info_group = ttk.LabelFrame(info_frame, text="📋 VST插件基本信息", padding="10")
        info_group.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_group.columnconfigure(1, weight=1)
        
        # 信息显示标签
        labels = [
            ("滤镜名称:", "filter_name_label"),
            ("插件ID:", "plugin_id_label"),
            ("滤镜类型:", "filter_kind_label"),
            ("启用状态:", "enabled_label"),
            ("插件路径:", "plugin_path_label")
        ]
        
        self.info_labels = {}
        for i, (text, var_name) in enumerate(labels):
            ttk.Label(info_group, text=text, style='Header.TLabel').grid(row=i, column=0, sticky=tk.W, pady=2)
            label = ttk.Label(info_group, text="未选择VST滤镜")
            label.grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
            self.info_labels[var_name] = label
        
        # VST详细信息
        details_group = ttk.LabelFrame(info_frame, text="📄 VST详细信息", padding="10")
        details_group.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        details_group.columnconfigure(0, weight=1)
        details_group.rowconfigure(0, weight=1)
        info_frame.rowconfigure(1, weight=1)
        
        self.vst_details_text = scrolledtext.ScrolledText(details_group, height=15, wrap=tk.WORD)
        self.vst_details_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.vst_details_text.insert(tk.END, "请选择一个VST滤镜查看详细信息...")
        
    def setup_parameters_tab(self):
        """设置参数列表标签页"""
        params_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(params_frame, text="📊 参数列表")
        
        # 参数控制区域
        param_control_frame = ttk.LabelFrame(params_frame, text="🎚️ 参数控制", padding="10")
        param_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        param_control_frame.columnconfigure(2, weight=1)
        
        ttk.Label(param_control_frame, text="参数过滤:").grid(row=0, column=0, padx=(0, 5))
        self.param_filter_combo = ttk.Combobox(param_control_frame, 
                                              values=["全部参数", "数值参数", "布尔参数", "字符串参数"],
                                              state="readonly", width=15)
        self.param_filter_combo.set("全部参数")
        self.param_filter_combo.grid(row=0, column=1, padx=(0, 20))
        self.param_filter_combo.bind('<<ComboboxSelected>>', self.filter_parameters)
        
        self.refresh_params_btn = ttk.Button(param_control_frame, text="🔄 刷新参数", 
                                           command=self.refresh_parameters, state='disabled')
        self.refresh_params_btn.grid(row=0, column=3, padx=(10, 0))
        
        # 参数表格
        params_columns = ('name', 'value', 'type', 'description')
        self.params_tree = ttk.Treeview(params_frame, columns=params_columns, show='headings', height=15)
        
        # 设置列标题和宽度
        headers = ['参数名称', '当前值', '类型', '描述']
        widths = [150, 100, 80, 300]
        
        for col, header, width in zip(params_columns, headers, widths):
            self.params_tree.heading(col, text=header)
            self.params_tree.column(col, width=width)
            
        self.params_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        params_frame.rowconfigure(1, weight=1)
        
        # 参数表格滚动条
        params_scrollbar = ttk.Scrollbar(params_frame, orient=tk.VERTICAL, command=self.params_tree.yview)
        params_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.params_tree.configure(yscrollcommand=params_scrollbar.set)
        
        self.params_tree.bind('<Double-1>', self.on_parameter_double_clicked)
        
    def setup_raw_data_tab(self):
        """设置原始数据标签页"""
        raw_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(raw_frame, text="📄 原始数据")
        
        # 原始数据显示
        self.raw_data_text = scrolledtext.ScrolledText(raw_frame, height=25, wrap=tk.WORD, font=('Consolas', 10))
        self.raw_data_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        raw_frame.rowconfigure(0, weight=1)
        raw_frame.columnconfigure(0, weight=1)
        
        self.raw_data_text.insert(tk.END, "这里将显示从OBS获取的原始JSON数据...")
        
    def setup_log_tab(self):
        """设置日志标签页"""
        log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(log_frame, text="📝 日志")
        
        # 日志控制
        log_control_frame = ttk.LabelFrame(log_frame, text="📝 日志控制", padding="10")
        log_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        log_control_frame.columnconfigure(1, weight=1)
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_cb = ttk.Checkbutton(log_control_frame, text="自动滚动", variable=self.auto_scroll_var)
        auto_scroll_cb.grid(row=0, column=0, padx=(0, 20))
        
        clear_log_btn = ttk.Button(log_control_frame, text="🗑️ 清空日志", command=self.clear_log)
        clear_log_btn.grid(row=0, column=2, padx=(10, 0))
        
        save_log_btn = ttk.Button(log_control_frame, text="💾 保存日志", command=self.save_log)
        save_log_btn.grid(row=0, column=3, padx=(10, 0))
        
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.rowconfigure(1, weight=1)
        
        # 初始日志
        self.log("🎛️ 真实VST插件ID检测器启动")
        self.log("=" * 60)
        self.log("ℹ️ 请先连接到OBS WebSocket")
        
    def setup_status_bar(self, parent):
        """设置底部状态栏"""
        status_frame = ttk.LabelFrame(parent, text="📊 状态信息", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪 - 等待连接OBS")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 统计信息
        self.stats_label = ttk.Label(status_frame, text="源: 0 | VST滤镜: 0 | 参数: 0", style='Success.TLabel')
        self.stats_label.grid(row=0, column=1, sticky=tk.E)

    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)

        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)

        # 同时更新状态栏
        self.status_label.config(text=message)

    def get_next_request_id(self):
        """获取下一个请求ID"""
        self.request_id_counter += 1
        return f"req-{int(time.time())}-{self.request_id_counter}"

    def toggle_obs_connection(self):
        """切换OBS连接状态"""
        if not self.is_connected:
            self.connect_to_obs()
        else:
            self.disconnect_from_obs()

    def connect_to_obs(self):
        """连接到OBS WebSocket"""
        obs_ws_url = self.ws_url_var.get()
        self.log(f"🔗 正在连接到 {obs_ws_url}...")
        self.connect_btn.config(state='disabled', text="连接中...")

        def connect_thread():
            try:
                # 创建WebSocket连接
                self.ws = websocket.create_connection(obs_ws_url, timeout=5)
                self.log("✅ WebSocket连接已建立，等待Hello消息...")

                # 接收Hello消息 (Opcode 0)
                hello_raw = self.ws.recv()
                hello_data = json.loads(hello_raw)
                self.log(f"📨 收到Hello: {hello_data}")

                if hello_data.get("op") != 0:
                    raise ValueError("收到的第一个消息不是Hello (Opcode 0)")

                obs_websocket_version = hello_data.get("d", {}).get("obsWebSocketVersion")
                rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
                authentication_required = hello_data.get("d", {}).get("authentication") is not None

                if authentication_required:
                    raise ConnectionAbortedError("OBS需要身份验证，但脚本未配置密码！请在OBS中禁用身份验证。")

                # 发送Identify消息 (Opcode 1)
                identify_payload = {
                    "op": 1,
                    "d": {
                        "rpcVersion": rpc_version,
                        "eventSubscriptions": 33  # 订阅所有事件
                    }
                }
                self.ws.send(json.dumps(identify_payload))
                self.log("📤 已发送Identify消息")

                # 接收Identified消息 (Opcode 2)
                identified_raw = self.ws.recv()
                identified_data = json.loads(identified_raw)
                self.log(f"📨 收到Identified: {identified_data}")

                if identified_data.get("op") != 2:
                    raise ValueError("收到的第二个消息不是Identified (Opcode 2)")

                # 连接成功
                self.root.after(0, self.on_connection_success, obs_websocket_version, rpc_version)

            except websocket.WebSocketTimeoutException:
                self.root.after(0, self.on_connection_error, "连接超时：请确保OBS已运行且WebSocket服务器已开启")
            except ConnectionRefusedError:
                self.root.after(0, self.on_connection_error, "连接被拒绝：请检查OBS WebSocket端口和设置")
            except Exception as e:
                self.root.after(0, self.on_connection_error, f"连接失败：{e}")

        # 在后台线程中连接
        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connection_success(self, obs_version, rpc_version):
        """连接成功回调"""
        self.is_connected = True
        self.connection_label.config(text="✅ 已连接到OBS", style='Success.TLabel')
        self.connect_btn.config(text="🔌 断开连接", state='normal')

        # 启用相关按钮
        self.refresh_sources_btn.config(state='normal')
        self.detect_filters_btn.config(state='normal')
        self.auto_detect_btn.config(state='normal')

        self.log(f"✅ 成功连接到OBS WebSocket! (OBS v{obs_version}, RPC v{rpc_version})")

        # 自动刷新源列表
        self.refresh_sources()

    def on_connection_error(self, error_msg):
        """连接错误回调"""
        self.is_connected = False
        self.connection_label.config(text="❌ 连接失败", style='Error.TLabel')
        self.connect_btn.config(text="🔗 连接OBS", state='normal')
        self.log(f"❌ {error_msg}")

    def disconnect_from_obs(self):
        """断开OBS连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None

        self.is_connected = False
        self.connection_label.config(text="❌ 未连接到OBS", style='Error.TLabel')
        self.connect_btn.config(text="🔗 连接OBS")

        # 禁用相关按钮
        self.refresh_sources_btn.config(state='disabled')
        self.detect_filters_btn.config(state='disabled')
        self.analyze_vst_btn.config(state='disabled')
        self.refresh_params_btn.config(state='disabled')
        self.export_btn.config(state='disabled')
        self.auto_detect_btn.config(state='disabled')

        # 清空数据
        self.source_combo.set('')
        self.source_combo['values'] = []
        for item in self.filters_tree.get_children():
            self.filters_tree.delete(item)

        self.log("🔌 已断开OBS连接")

    def send_request_and_get_response(self, request_type, request_data=None, timeout=5):
        """发送请求并等待响应"""
        if not self.is_connected or not self.ws:
            self.log("❌ 未连接到OBS，无法发送请求")
            return None

        # 生成唯一的请求ID
        request_id = self.get_next_request_id()

        payload = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data if request_data is not None else {}
            }
        }

        try:
            self.ws.send(json.dumps(payload))
            self.log(f"📤 已发送请求: {request_type} (ID: {request_id})")

            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)

                    if response_data.get("op") == 7:  # RequestResponse
                        response_d = response_data.get("d", {})
                        if response_d.get("requestId") == request_id:
                            self.log(f"📨 收到响应: {request_type} (ID: {request_id})")
                            return response_d

                except websocket.WebSocketTimeoutException:
                    continue
                except json.JSONDecodeError:
                    self.log("❌ 解析WebSocket消息失败")
                    continue

            self.log(f"⏰ 等待响应超时: {request_type} (ID: {request_id})")
            return None

        except Exception as e:
            self.log(f"❌ 发送请求失败: {e}")
            return None

    def refresh_sources(self):
        """刷新音频源列表"""
        if not self.is_connected:
            return

        self.log("🔄 正在刷新音频源列表...")

        # 获取输入源列表
        response_d = self.send_request_and_get_response("GetInputList")

        if response_d and response_d.get('requestStatus', {}).get('result') is True:
            inputs_list = response_d.get('responseData', {}).get('inputs', [])
            self.log(f"📋 获取到 {len(inputs_list)} 个输入源")

            # 过滤音频源
            audio_sources = []
            for input_item in inputs_list:
                input_name = input_item.get('inputName', '')
                input_kind = input_item.get('inputKind', '')

                # 检查是否为音频源
                if any(audio_type in input_kind.lower() for audio_type in
                      ['audio', 'mic', 'wasapi', 'pulse', 'alsa', 'coreaudio']):
                    audio_sources.append(input_name)
                    self.log(f"  🎤 音频源: {input_name} (类型: {input_kind})")

            # 更新下拉框
            self.source_combo['values'] = audio_sources
            if audio_sources:
                self.source_combo.set(audio_sources[0])
                self.log(f"✅ 找到 {len(audio_sources)} 个音频源")
            else:
                self.source_combo.set('')
                self.log("⚠️ 未找到音频源")

            # 存储所有源数据
            self.sources_data = {item.get('inputName', ''): item for item in inputs_list}

        else:
            self.log("❌ 获取源列表失败")
            self.source_combo['values'] = []
            self.source_combo.set('')

    def on_source_selected(self, event=None):
        """音频源选择改变时的处理"""
        if self.is_connected and self.source_combo.get():
            self.log(f"📋 选择音频源: {self.source_combo.get()}")
            self.detect_filters()

    def detect_filters(self):
        """检测选中音频源的滤镜"""
        source_name = self.source_combo.get()
        if not source_name or not self.is_connected:
            return

        self.log(f"🔍 正在检测音频源 '{source_name}' 的滤镜...")

        # 获取源的滤镜列表
        response_d = self.send_request_and_get_response("GetSourceFilterList", {
            "sourceName": source_name
        })

        if response_d and response_d.get('requestStatus', {}).get('result') is True:
            filters_list = response_d.get('responseData', {}).get('filters', [])
            self.log(f"📋 找到 {len(filters_list)} 个滤镜")

            # 清空当前列表
            for item in self.filters_tree.get_children():
                self.filters_tree.delete(item)

            # 存储滤镜数据
            self.current_source_filters = {}
            vst_count = 0

            for filter_item in filters_list:
                filter_name = filter_item.get('filterName', '')
                filter_kind = filter_item.get('filterKind', '')
                filter_enabled = filter_item.get('filterEnabled', False)

                # 存储滤镜数据
                self.current_source_filters[filter_name] = filter_item

                # 检查是否为VST滤镜
                is_vst = 'vst' in filter_kind.lower()
                if is_vst:
                    vst_count += 1

                # 添加到树形控件
                enabled_text = "✅ 启用" if filter_enabled else "❌ 禁用"
                item_id = self.filters_tree.insert('', tk.END, values=(filter_name, filter_kind, enabled_text))

                # 为VST滤镜添加特殊标记
                if is_vst:
                    self.filters_tree.set(item_id, 'vst_filter', 'true')
                    self.log(f"  🎛️ VST滤镜: {filter_name} ({filter_kind})")
                else:
                    self.log(f"  ⚪ 其他滤镜: {filter_name} ({filter_kind})")

            self.log(f"✅ 检测完成，找到 {vst_count} 个VST滤镜")
            self.update_stats()

        else:
            self.log(f"❌ 获取滤镜列表失败")

    def on_filter_selected(self, event=None):
        """滤镜选择改变时的处理"""
        selected_items = self.filters_tree.selection()
        if not selected_items:
            self.analyze_vst_btn.config(state='disabled')
            self.refresh_params_btn.config(state='disabled')
            return

        # 获取选中的滤镜
        item_id = selected_items[0]
        filter_name = self.filters_tree.item(item_id)['values'][0]
        filter_kind = self.filters_tree.item(item_id)['values'][1]

        # 检查是否为VST滤镜
        is_vst = 'vst' in filter_kind.lower()

        if is_vst:
            self.analyze_vst_btn.config(state='normal')
            self.refresh_params_btn.config(state='normal')
            self.log(f"🎛️ 选择VST滤镜: {filter_name}")
        else:
            self.analyze_vst_btn.config(state='disabled')
            self.refresh_params_btn.config(state='disabled')
            self.log(f"ℹ️ 选择的不是VST滤镜: {filter_name}")

    def analyze_vst_filter(self):
        """分析选中的VST滤镜"""
        selected_items = self.filters_tree.selection()
        if not selected_items:
            return

        item_id = selected_items[0]
        filter_name = self.filters_tree.item(item_id)['values'][0]
        source_name = self.source_combo.get()

        self.log(f"📊 正在分析VST滤镜: {filter_name}")

        # 获取滤镜详细信息
        response_d = self.send_request_and_get_response("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response_d and response_d.get('requestStatus', {}).get('result') is True:
            filter_data = response_d.get('responseData', {})
            self.current_plugin_data = filter_data

            # 更新VST信息显示
            self.update_vst_info_display(filter_data)

            # 切换到VST信息标签页
            self.notebook.select(0)

            # 显示原始数据
            self.show_raw_data(filter_data)

            # 启用导出按钮
            self.export_btn.config(state='normal')

            self.log(f"✅ VST滤镜分析完成: {filter_name}")

        else:
            self.log(f"❌ 获取VST滤镜信息失败: {filter_name}")

    def update_vst_info_display(self, filter_data):
        """更新VST信息显示"""
        filter_name = filter_data.get('filterName', 'Unknown')
        filter_kind = filter_data.get('filterKind', 'Unknown')
        filter_enabled = filter_data.get('filterEnabled', False)
        filter_settings = filter_data.get('filterSettings', {})

        # 更新基本信息标签
        self.info_labels['filter_name_label'].config(text=filter_name)
        self.info_labels['plugin_id_label'].config(text=filter_kind)
        self.info_labels['filter_kind_label'].config(text=filter_kind)
        self.info_labels['enabled_label'].config(text="✅ 启用" if filter_enabled else "❌ 禁用")

        # 尝试从设置中获取插件路径
        plugin_path = filter_settings.get('plugin_path', filter_settings.get('dll_path', '未知'))
        self.info_labels['plugin_path_label'].config(text=plugin_path)

        # 更新详细信息
        details = f"VST滤镜详细信息\n"
        details += "=" * 50 + "\n\n"
        details += f"滤镜名称: {filter_name}\n"
        details += f"插件ID: {filter_kind}\n"
        details += f"启用状态: {'启用' if filter_enabled else '禁用'}\n"
        details += f"插件路径: {plugin_path}\n\n"

        details += "滤镜设置参数:\n"
        details += "-" * 30 + "\n"

        param_count = 0
        for param_name, param_value in filter_settings.items():
            param_type = type(param_value).__name__
            details += f"• {param_name}: {param_value} ({param_type})\n"
            param_count += 1

        details += f"\n总参数数量: {param_count}"

        self.vst_details_text.delete(1.0, tk.END)
        self.vst_details_text.insert(tk.END, details)

        # 显示参数列表
        self.display_parameters(filter_settings)

    def display_parameters(self, parameters):
        """显示参数列表"""
        # 清空参数表格
        for item in self.params_tree.get_children():
            self.params_tree.delete(item)

        if not parameters:
            return

        for param_name, param_value in parameters.items():
            param_type = type(param_value).__name__

            # 生成参数描述
            description = self.generate_parameter_description(param_name, param_value, param_type)

            # 添加到参数表格
            self.params_tree.insert('', tk.END, values=(
                param_name,
                str(param_value),
                param_type,
                description
            ))

        self.log(f"📊 显示了 {len(parameters)} 个参数")

    def generate_parameter_description(self, param_name, param_value, param_type):
        """生成参数描述"""
        # 基于参数名称推测功能
        name_lower = param_name.lower()

        if 'pitch' in name_lower:
            return "音调调节参数"
        elif 'gain' in name_lower or 'volume' in name_lower:
            return "音量/增益参数"
        elif 'mix' in name_lower or 'wet' in name_lower:
            return "干湿混合比例"
        elif 'drive' in name_lower or 'distortion' in name_lower:
            return "驱动/失真强度"
        elif 'tone' in name_lower:
            return "音色调节参数"
        elif 'reverb' in name_lower or 'room' in name_lower:
            return "混响相关参数"
        elif 'delay' in name_lower:
            return "延迟相关参数"
        elif 'filter' in name_lower:
            return "滤波器参数"
        elif 'bypass' in name_lower or 'enable' in name_lower:
            return "开关/旁路参数"
        elif param_name.startswith('param_'):
            return f"通用参数 {param_name.split('_')[-1]}"
        else:
            return f"{param_type}类型参数"
