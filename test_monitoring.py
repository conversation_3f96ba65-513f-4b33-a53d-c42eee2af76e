#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的VST参数监控功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_import():
    """测试导入"""
    try:
        import vst_parameter_tester
        print("✅ 成功导入 vst_parameter_tester")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def show_usage_tips():
    """显示使用提示"""
    tips = """
🧪 VST参数测试器 - 改进后的监控功能使用指南

📡 实时监控功能改进:
1. 不再需要先运行"探测参数"
2. 会自动检测所有参数的变化
3. 更敏感的变化检测 (0.001精度)
4. 每0.5秒检查一次，响应更快

🔄 新增功能:
1. "手动刷新" - 立即查看当前所有参数状态
2. 改进的错误处理和重连机制
3. 更详细的参数变化日志

📋 使用步骤:
1. 启动程序
2. 连接OBS
3. 设置正确的音频源和VST滤镜名称
4. 点击"手动刷新"查看当前参数
5. 勾选"实时监控"开始监控
6. 在OBS中调节VST插件参数
7. 观察程序中的实时变化日志

💡 提示:
- 如果监控没有检测到变化，请检查音频源和滤镜名称是否正确
- 使用"手动刷新"可以验证连接是否正常
- 监控会显示所有参数的初始值，然后跟踪变化
"""
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    messagebox.showinfo("使用指南", tips)
    root.destroy()

def main():
    print("🧪 VST参数测试器 - 监控功能测试")
    print("=" * 50)
    
    # 测试导入
    if not test_import():
        print("❌ 无法导入模块，请检查文件是否存在")
        return
    
    # 显示使用提示
    print("📋 显示使用指南...")
    show_usage_tips()
    
    # 启动主程序
    print("🚀 启动VST参数测试器...")
    try:
        from vst_parameter_tester import VSTParameterTester
        app = VSTParameterTester()
        app.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
