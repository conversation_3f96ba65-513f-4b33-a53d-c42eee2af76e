#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ VST插件ID检测器 (Tkinter版本)
专门用于检测OBS中VST2x插件的ID和参数信息的可视化工具
使用Python内置的tkinter库，无需额外安装依赖
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import time
from datetime import datetime
import threading

class VST_Plugin_ID_Detector_TK:
    """VST插件ID检测器主窗口 (Tkinter版本)"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ VST插件ID检测器 - OBS VST2x插件分析工具")
        self.root.geometry("1200x800")
        
        # 设置样式
        self.setup_styles()
        
        # 模拟OBS连接状态
        self.is_connected = False
        self.obs_client = None
        
        # 检测到的插件数据
        self.detected_plugins = {}
        self.current_plugin_data = {}
        
        # 设置模拟数据
        self.setup_mock_data()
        
        # 初始化UI
        self.setup_ui()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 顶部控制区域
        self.setup_control_area(main_frame)
        
        # 主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # 左侧面板
        self.setup_left_panel(content_frame)
        
        # 右侧面板
        self.setup_right_panel(content_frame)
        
        # 底部状态栏
        self.setup_status_bar(main_frame)
        
    def setup_control_area(self, parent):
        """设置顶部控制区域"""
        control_frame = ttk.LabelFrame(parent, text="🔧 连接和控制", padding="10")
        control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(4, weight=1)
        
        # OBS连接状态
        self.connection_label = ttk.Label(control_frame, text="❌ 未连接到OBS", style='Error.TLabel')
        self.connection_label.grid(row=0, column=0, padx=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="🔗 连接OBS", command=self.toggle_obs_connection)
        self.connect_btn.grid(row=0, column=1, padx=(0, 20))
        
        # 音频源选择
        ttk.Label(control_frame, text="音频源:").grid(row=0, column=2, padx=(0, 5))
        self.source_combo = ttk.Combobox(control_frame, values=["麦克风", "桌面音频", "音频输入捕获", "媒体源"], 
                                        state="readonly", width=15)
        self.source_combo.set("麦克风")
        self.source_combo.grid(row=0, column=3, padx=(0, 10))
        self.source_combo.bind('<<ComboboxSelected>>', self.on_source_changed)
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(control_frame, text="🔄 刷新", command=self.refresh_plugins, state='disabled')
        self.refresh_btn.grid(row=0, column=5, padx=(10, 0))
        
    def setup_left_panel(self, parent):
        """设置左侧面板"""
        left_frame = ttk.Frame(parent, width=400)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(0, weight=1)
        
        # VST插件列表
        plugin_frame = ttk.LabelFrame(left_frame, text="🎛️ 检测到的VST2x插件", padding="10")
        plugin_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        plugin_frame.columnconfigure(0, weight=1)
        plugin_frame.rowconfigure(0, weight=1)
        
        # 插件列表
        columns = ('name', 'type', 'status')
        self.plugin_tree = ttk.Treeview(plugin_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        self.plugin_tree.heading('name', text='插件名称')
        self.plugin_tree.heading('type', text='类型')
        self.plugin_tree.heading('status', text='状态')
        
        # 设置列宽
        self.plugin_tree.column('name', width=150)
        self.plugin_tree.column('type', width=120)
        self.plugin_tree.column('status', width=100)
        
        self.plugin_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.plugin_tree.bind('<<TreeviewSelect>>', self.on_plugin_selected)
        
        # 滚动条
        plugin_scrollbar = ttk.Scrollbar(plugin_frame, orient=tk.VERTICAL, command=self.plugin_tree.yview)
        plugin_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.plugin_tree.configure(yscrollcommand=plugin_scrollbar.set)
        
        # 插件操作按钮
        plugin_btn_frame = ttk.Frame(plugin_frame)
        plugin_btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        plugin_btn_frame.columnconfigure(0, weight=1)
        plugin_btn_frame.columnconfigure(1, weight=1)
        
        self.detect_btn = ttk.Button(plugin_btn_frame, text="🔍 检测插件", command=self.detect_plugins, state='disabled')
        self.detect_btn.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        
        self.analyze_btn = ttk.Button(plugin_btn_frame, text="📊 分析参数", command=self.analyze_selected_plugin, state='disabled')
        self.analyze_btn.grid(row=0, column=1, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 快速操作
        quick_frame = ttk.LabelFrame(left_frame, text="⚡ 快速操作", padding="10")
        quick_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        quick_frame.columnconfigure(0, weight=1)
        
        self.auto_detect_btn = ttk.Button(quick_frame, text="🚀 一键检测所有VST插件", 
                                         command=self.auto_detect_all_plugins, state='disabled')
        self.auto_detect_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.export_btn = ttk.Button(quick_frame, text="💾 导出检测结果", 
                                    command=self.export_results, state='disabled')
        self.export_btn.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
    def setup_right_panel(self, parent):
        """设置右侧面板"""
        right_frame = ttk.Frame(parent)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=1)
        
        # 使用Notebook创建标签页
        self.notebook = ttk.Notebook(right_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 插件信息标签页
        self.setup_plugin_info_tab()
        
        # 参数列表标签页
        self.setup_parameters_tab()
        
        # 日志标签页
        self.setup_log_tab()
        
    def setup_plugin_info_tab(self):
        """设置插件信息标签页"""
        info_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(info_frame, text="🔍 插件信息")
        
        # 插件基本信息
        info_group = ttk.LabelFrame(info_frame, text="📋 插件基本信息", padding="10")
        info_group.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_group.columnconfigure(1, weight=1)
        
        # 信息显示标签
        ttk.Label(info_group, text="插件名称:", style='Header.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        self.plugin_name_label = ttk.Label(info_group, text="未选择插件", style='Success.TLabel')
        self.plugin_name_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(info_group, text="插件ID:", style='Header.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        self.plugin_id_label = ttk.Label(info_group, text="-", style='Warning.TLabel')
        self.plugin_id_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(info_group, text="滤镜类型:", style='Header.TLabel').grid(row=2, column=0, sticky=tk.W, pady=2)
        self.filter_type_label = ttk.Label(info_group, text="-")
        self.filter_type_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(info_group, text="参数数量:", style='Header.TLabel').grid(row=3, column=0, sticky=tk.W, pady=2)
        self.param_count_label = ttk.Label(info_group, text="-")
        self.param_count_label.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(info_group, text="插件路径:", style='Header.TLabel').grid(row=4, column=0, sticky=tk.W, pady=2)
        self.plugin_path_label = ttk.Label(info_group, text="-", wraplength=400)
        self.plugin_path_label.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 插件详细信息
        details_group = ttk.LabelFrame(info_frame, text="📄 详细信息", padding="10")
        details_group.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        details_group.columnconfigure(0, weight=1)
        details_group.rowconfigure(0, weight=1)
        info_frame.rowconfigure(1, weight=1)
        
        self.plugin_details_text = scrolledtext.ScrolledText(details_group, height=15, wrap=tk.WORD)
        self.plugin_details_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.plugin_details_text.insert(tk.END, "请选择一个插件查看详细信息...")
        
    def setup_parameters_tab(self):
        """设置参数列表标签页"""
        params_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(params_frame, text="📊 参数列表")
        
        # 参数控制区域
        param_control_frame = ttk.LabelFrame(params_frame, text="🎚️ 参数控制", padding="10")
        param_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        param_control_frame.columnconfigure(2, weight=1)
        
        ttk.Label(param_control_frame, text="参数过滤:").grid(row=0, column=0, padx=(0, 5))
        self.param_filter_combo = ttk.Combobox(param_control_frame, 
                                              values=["全部参数", "数值参数", "布尔参数", "字符串参数"],
                                              state="readonly", width=15)
        self.param_filter_combo.set("全部参数")
        self.param_filter_combo.grid(row=0, column=1, padx=(0, 20))
        self.param_filter_combo.bind('<<ComboboxSelected>>', self.filter_parameters)
        
        self.refresh_params_btn = ttk.Button(param_control_frame, text="🔄 刷新参数", 
                                           command=self.refresh_parameters, state='disabled')
        self.refresh_params_btn.grid(row=0, column=3, padx=(10, 0))
        
        # 参数表格
        params_columns = ('name', 'id', 'value', 'type', 'range', 'description')
        self.params_tree = ttk.Treeview(params_frame, columns=params_columns, show='headings', height=15)
        
        # 设置列标题和宽度
        headers = ['参数名称', '参数ID', '当前值', '类型', '范围', '描述']
        widths = [120, 80, 80, 60, 100, 200]
        
        for col, header, width in zip(params_columns, headers, widths):
            self.params_tree.heading(col, text=header)
            self.params_tree.column(col, width=width)
            
        self.params_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        params_frame.rowconfigure(1, weight=1)
        
        # 参数表格滚动条
        params_scrollbar = ttk.Scrollbar(params_frame, orient=tk.VERTICAL, command=self.params_tree.yview)
        params_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.params_tree.configure(yscrollcommand=params_scrollbar.set)
        
        self.params_tree.bind('<Double-1>', self.on_parameter_double_clicked)
        
    def setup_log_tab(self):
        """设置日志标签页"""
        log_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(log_frame, text="📝 日志")
        
        # 日志控制
        log_control_frame = ttk.LabelFrame(log_frame, text="📝 日志控制", padding="10")
        log_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        log_control_frame.columnconfigure(1, weight=1)
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_cb = ttk.Checkbutton(log_control_frame, text="自动滚动", variable=self.auto_scroll_var)
        auto_scroll_cb.grid(row=0, column=0, padx=(0, 20))
        
        clear_log_btn = ttk.Button(log_control_frame, text="🗑️ 清空日志", command=self.clear_log)
        clear_log_btn.grid(row=0, column=2, padx=(10, 0))
        
        save_log_btn = ttk.Button(log_control_frame, text="💾 保存日志", command=self.save_log)
        save_log_btn.grid(row=0, column=3, padx=(10, 0))
        
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.rowconfigure(1, weight=1)
        
        # 初始日志
        self.log("🎛️ VST插件ID检测器启动")
        self.log("=" * 50)
        
    def setup_status_bar(self, parent):
        """设置底部状态栏"""
        status_frame = ttk.LabelFrame(parent, text="📊 状态信息", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 统计信息
        self.stats_label = ttk.Label(status_frame, text="插件: 0 | 参数: 0", style='Success.TLabel')
        self.stats_label.grid(row=0, column=1, sticky=tk.E)

    def setup_mock_data(self):
        """设置模拟数据（实际使用时会连接真实OBS）"""
        self.mock_plugins = {
            "麦克风": {
                "Graillon音调": {
                    "plugin_id": "Auburn Sounds Graillon 3-64",
                    "filter_type": "vst_filter",
                    "plugin_path": "C:/Program Files/VstPlugins/Auburn Sounds/Graillon 3-64.dll",
                    "parameters": {
                        "pitch": {"value": 0.0, "type": "float", "range": [-12.0, 12.0], "description": "音调偏移（半音）"},
                        "formant": {"value": 100.0, "type": "float", "range": [50.0, 150.0], "description": "共振峰调节"},
                        "mix": {"value": 100.0, "type": "float", "range": [0.0, 100.0], "description": "干湿混合比例"},
                        "bypass": {"value": False, "type": "bool", "range": [False, True], "description": "旁路开关"},
                        "param_0": {"value": 0.0, "type": "float", "range": [0.0, 1.0], "description": "通用参数0"},
                        "param_1": {"value": 0.5, "type": "float", "range": [0.0, 1.0], "description": "通用参数1"}
                    }
                },
                "TSE808失真": {
                    "plugin_id": "TSE_808_2.0_x64",
                    "filter_type": "vst_filter",
                    "plugin_path": "C:/Program Files/VstPlugins/TSE/TSE_808_2.0_x64.dll",
                    "parameters": {
                        "drive": {"value": 30.0, "type": "float", "range": [0.0, 100.0], "description": "驱动强度"},
                        "tone": {"value": 50.0, "type": "float", "range": [0.0, 100.0], "description": "音色调节"},
                        "level": {"value": 80.0, "type": "float", "range": [0.0, 100.0], "description": "输出电平"},
                        "enabled": {"value": True, "type": "bool", "range": [False, True], "description": "启用状态"},
                        "Drive": {"value": 30.0, "type": "float", "range": [0.0, 100.0], "description": "驱动强度（大写）"},
                        "Tone": {"value": 50.0, "type": "float", "range": [0.0, 100.0], "description": "音色调节（大写）"}
                    }
                },
                "TAL混响": {
                    "plugin_id": "TAL-Reverb-4-64",
                    "filter_type": "vst_filter",
                    "plugin_path": "C:/Program Files/VstPlugins/TAL/TAL-Reverb-4-64.dll",
                    "parameters": {
                        "roomsize": {"value": 40.0, "type": "float", "range": [0.0, 100.0], "description": "房间大小"},
                        "damping": {"value": 60.0, "type": "float", "range": [0.0, 100.0], "description": "阻尼系数"},
                        "mix": {"value": 25.0, "type": "float", "range": [0.0, 100.0], "description": "混响混合"},
                        "predelay": {"value": 0.0, "type": "float", "range": [0.0, 200.0], "description": "预延迟"},
                        "RoomSize": {"value": 40.0, "type": "float", "range": [0.0, 100.0], "description": "房间大小（大写）"},
                        "Damping": {"value": 60.0, "type": "float", "range": [0.0, 100.0], "description": "阻尼系数（大写）"}
                    }
                }
            }
        }

    def log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)

        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)

        # 同时更新状态栏
        self.status_label.config(text=message)

    def toggle_obs_connection(self):
        """切换OBS连接状态"""
        if not self.is_connected:
            # 模拟连接过程
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state='disabled')

            # 使用线程模拟连接延迟
            def connect_thread():
                time.sleep(2)  # 模拟连接延迟
                self.root.after(0, self.complete_connection)

            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            # 断开连接
            self.disconnect_obs()

    def complete_connection(self):
        """完成OBS连接"""
        self.is_connected = True
        self.connection_label.config(text="✅ 已连接到OBS", style='Success.TLabel')
        self.connect_btn.config(text="🔌 断开连接", state='normal')

        # 启用相关按钮
        self.refresh_btn.config(state='normal')
        self.detect_btn.config(state='normal')
        self.auto_detect_btn.config(state='normal')

        self.log("✅ 成功连接到OBS WebSocket")

        # 自动刷新插件列表
        self.refresh_plugins()

    def disconnect_obs(self):
        """断开OBS连接"""
        self.is_connected = False
        self.connection_label.config(text="❌ 未连接到OBS", style='Error.TLabel')
        self.connect_btn.config(text="🔗 连接OBS")

        # 禁用相关按钮
        self.refresh_btn.config(state='disabled')
        self.detect_btn.config(state='disabled')
        self.auto_detect_btn.config(state='disabled')
        self.analyze_btn.config(state='disabled')
        self.refresh_params_btn.config(state='disabled')
        self.export_btn.config(state='disabled')

        self.log("🔌 已断开OBS连接")

    def on_source_changed(self, event=None):
        """音频源改变时的处理"""
        if self.is_connected:
            self.log(f"📋 切换到音频源: {self.source_combo.get()}")
            self.refresh_plugins()

    def refresh_plugins(self):
        """刷新插件列表"""
        if not self.is_connected:
            return

        self.log("🔄 正在刷新VST插件列表...")
        source_name = self.source_combo.get()

        # 清空当前列表
        for item in self.plugin_tree.get_children():
            self.plugin_tree.delete(item)

        # 获取模拟数据
        if source_name in self.mock_plugins:
            plugins = self.mock_plugins[source_name]

            for filter_name, plugin_data in plugins.items():
                # 插件类型
                plugin_id = plugin_data.get("plugin_id", "Unknown")

                # 状态
                param_count = len(plugin_data.get("parameters", {}))
                status = f"✅ {param_count}个参数"

                # 插入到树形控件
                item_id = self.plugin_tree.insert('', tk.END, values=(filter_name, plugin_id, status))

                # 存储完整数据
                self.plugin_tree.set(item_id, 'data', json.dumps(plugin_data))

            self.log(f"✅ 发现 {len(plugins)} 个VST插件")
            self.update_stats()
        else:
            self.log(f"ℹ️ 音频源 '{source_name}' 没有VST插件")

    def update_stats(self):
        """更新统计信息"""
        plugin_count = len(self.plugin_tree.get_children())
        total_params = 0

        for item_id in self.plugin_tree.get_children():
            try:
                data_str = self.plugin_tree.set(item_id, 'data')
                if data_str:
                    plugin_data = json.loads(data_str)
                    total_params += len(plugin_data.get("parameters", {}))
            except:
                pass

        self.stats_label.config(text=f"插件: {plugin_count} | 参数: {total_params}")

    def on_plugin_selected(self, event=None):
        """插件选择改变时的处理"""
        selected_items = self.plugin_tree.selection()
        if not selected_items:
            self.analyze_btn.config(state='disabled')
            self.refresh_params_btn.config(state='disabled')
            return

        # 获取选中的插件数据
        item_id = selected_items[0]
        plugin_name = self.plugin_tree.item(item_id)['values'][0]

        try:
            data_str = self.plugin_tree.set(item_id, 'data')
            plugin_data = json.loads(data_str) if data_str else {}
        except:
            plugin_data = {}

        if plugin_data:
            self.current_plugin_data = plugin_data
            self.analyze_btn.config(state='normal')
            self.refresh_params_btn.config(state='normal')

            # 更新插件信息显示
            self.update_plugin_info_display(plugin_name, plugin_data)

    def update_plugin_info_display(self, plugin_name, plugin_data):
        """更新插件信息显示"""
        self.plugin_name_label.config(text=plugin_name)
        self.plugin_id_label.config(text=plugin_data.get("plugin_id", "Unknown"))
        self.filter_type_label.config(text=plugin_data.get("filter_type", "Unknown"))

        params = plugin_data.get("parameters", {})
        self.param_count_label.config(text=str(len(params)))
        self.plugin_path_label.config(text=plugin_data.get("plugin_path", "Unknown"))

        # 更新详细信息
        details = f"插件名称: {plugin_name}\n"
        details += f"插件ID: {plugin_data.get('plugin_id', 'Unknown')}\n"
        details += f"滤镜类型: {plugin_data.get('filter_type', 'Unknown')}\n"
        details += f"插件路径: {plugin_data.get('plugin_path', 'Unknown')}\n"
        details += f"参数数量: {len(params)}\n\n"
        details += "参数概览:\n"
        details += "-" * 30 + "\n"

        for param_name, param_info in params.items():
            param_type = param_info.get("type", "unknown")
            param_value = param_info.get("value", "N/A")
            details += f"• {param_name}: {param_value} ({param_type})\n"

        self.plugin_details_text.delete(1.0, tk.END)
        self.plugin_details_text.insert(tk.END, details)
