#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Python环境测试")
print("正在导入tkinter...")

try:
    import tkinter as tk
    print("✅ tkinter导入成功")
except ImportError as e:
    print(f"❌ tkinter导入失败: {e}")

try:
    import websocket
    print("✅ websocket导入成功")
except ImportError as e:
    print(f"❌ websocket导入失败: {e}")

try:
    import json
    print("✅ json导入成功")
except ImportError as e:
    print(f"❌ json导入失败: {e}")

print("环境测试完成")
