# 🎛️ OBS VST插件控制使用指南

## 📋 概述

这套脚本可以通过OBS API自动控制以下三个VST插件的参数：

1. **🎭 Auburn Sounds Graillon 3** - 变声/音调处理
2. **🌊 TAL-Reverb-4** - 混响效果  
3. **🔥 TSE 808 v2.0** - 失真/过载效果

## 📁 文件说明

### 1. `VST三插件控制器.lua`
- **功能**: 主要的控制脚本，可以同时控制三个VST插件
- **特点**: 
  - 随机参数变化
  - 可调节的时间间隔
  - 独立的插件开关
  - 详细的参数范围设置

### 2. `VST参数测试器.lua`
- **功能**: 用于测试和发现VST插件的参数名称
- **用途**: 在使用主控制器之前，先确定正确的参数名称

## 🚀 快速开始

### 第一步：安装VST插件到OBS

1. 确保你的三个VST插件已正确安装到OBS
2. 在音频源上添加VST滤镜：
   - 右键音频源 → 滤镜 → 添加 → VST 2.x 插件
   - 选择对应的VST插件文件

### 第二步：确定参数名称

1. 加载 `VST参数测试器.lua` 脚本
2. 选择包含VST滤镜的音频源
3. 输入VST滤镜的确切名称（如"Graillon"、"TAL-Reverb"等）
4. 点击"开始参数测试"
5. 查看日志输出，记录有效的参数名称

### 第三步：配置主控制器

1. 加载 `VST三插件控制器.lua` 脚本
2. 在脚本属性中配置：
   - 选择音频源
   - 设置各插件的滤镜名称
   - 调整参数范围
   - 设置更新间隔
3. 启用"VST自动控制"

## ⚙️ 详细配置

### Graillon 3 参数

| 参数名 | 说明 | 建议范围 |
|--------|------|----------|
| pitch | 音调偏移 | -3.0 到 3.0 |
| formant | 共振峰调节 | 80.0 到 120.0 |
| mix | 干湿混合 | 30.0 到 70.0 |

### TAL-Reverb-4 参数

| 参数名 | 说明 | 建议范围 |
|--------|------|----------|
| roomsize | 房间大小 | 20.0 到 80.0 |
| damping | 阻尼控制 | 10.0 到 60.0 |
| mix | 混响混合 | 15.0 到 45.0 |

### TSE 808 参数

| 参数名 | 说明 | 建议范围 |
|--------|------|----------|
| drive | 驱动强度 | 20.0 到 70.0 |
| tone | 音调控制 | 30.0 到 80.0 |
| level | 输出电平 | 40.0 到 80.0 |

## 🔧 故障排除

### 问题1：参数不生效
**可能原因**：
- 滤镜名称不正确
- 参数名称不匹配
- VST插件未正确加载

**解决方案**：
1. 使用参数测试器确认正确的参数名称
2. 检查滤镜名称是否与OBS中显示的完全一致
3. 重新加载VST插件

### 问题2：音频效果异常
**可能原因**：
- 参数范围设置过大
- 更新频率过高

**解决方案**：
1. 减小参数变化范围
2. 增加更新间隔时间
3. 先测试单个插件

### 问题3：脚本报错
**可能原因**：
- 音频源不存在
- 滤镜未找到

**解决方案**：
1. 确认音频源名称正确
2. 确认VST滤镜已添加到音频源
3. 查看OBS脚本日志获取详细错误信息

## 💡 使用技巧

### 1. 渐进式测试
- 先启用一个插件进行测试
- 确认效果正常后再启用其他插件
- 逐步调整参数范围

### 2. 参数范围建议
- **保守范围**: 避免过度的音频效果
- **测试范围**: 先用小范围测试，再逐步扩大
- **实时调整**: 根据实际效果调整参数范围

### 3. 性能优化
- 适当增加更新间隔，避免过于频繁的参数变化
- 不需要的插件可以禁用以节省资源

## 📝 自定义扩展

如果你想添加其他VST插件的支持，可以：

1. 使用参数测试器找到新插件的参数名称
2. 在主控制器中添加新的参数组
3. 创建对应的控制函数

## ⚠️ 注意事项

1. **音量控制**: 测试时建议降低音量，避免突然的音频变化
2. **直播使用**: 在直播前充分测试，确保效果符合预期
3. **备份设置**: 记录好工作的参数配置，便于恢复
4. **兼容性**: 不同版本的VST插件参数名称可能不同

## 🆘 获取帮助

如果遇到问题：
1. 查看OBS脚本日志
2. 启用调试模式获取详细信息
3. 确认VST插件版本和OBS版本兼容性

---

**祝你使用愉快！🎉**
