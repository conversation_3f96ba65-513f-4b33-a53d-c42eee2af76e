#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ VST直接控制测试
测试直接通过OBS WebSocket控制VST插件参数
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import websocket
import threading
import time
import base64
import struct
from datetime import datetime

class VSTDirectControlTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ VST直接控制测试")
        self.root.geometry("900x600")
        
        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # VST数据
        self.current_chunk_data = ""
        self.baseline_chunk = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # VST控制区域
        control_frame = ttk.LabelFrame(main_frame, text="🎛️ VST控制测试", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：基本信息
        row1 = ttk.Frame(control_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(row1, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row1, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(row1, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        self.get_baseline_btn = ttk.Button(row1, text="📊 获取基准", command=self.get_baseline, state="disabled")
        self.get_baseline_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # 第二行：参数测试
        row2 = ttk.Frame(control_frame)
        row2.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(row2, text="测试参数:").pack(side=tk.LEFT)
        self.param_var = tk.StringVar(value="0")
        ttk.Entry(row2, textvariable=self.param_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row2, text="测试值:").pack(side=tk.LEFT)
        self.value_var = tk.StringVar(value="0.5")
        ttk.Entry(row2, textvariable=self.value_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        self.test_btn = ttk.Button(row2, text="🧪 测试设置", command=self.test_parameter, state="disabled")
        self.test_btn.pack(side=tk.LEFT, padx=(10, 5))
        
        self.compare_btn = ttk.Button(row2, text="🔍 对比变化", command=self.compare_changes, state="disabled")
        self.compare_btn.pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🎛️ VST直接控制测试工具启动")
        self.log("请先连接到OBS，然后获取VST插件的基准数据")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # 接收Hello消息
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    # 发送Identify消息
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    # 接收Identified消息
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        self.get_baseline_btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        self.get_baseline_btn.config(state="disabled")
        self.test_btn.config(state="disabled")
        self.compare_btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None
            
    def get_baseline(self):
        """获取基准数据"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()
        
        self.log(f"📊 获取VST插件基准数据: {filter_name}")
        
        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        
        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})
            self.baseline_chunk = settings.get('chunk_data', '')
            
            self.log("✅ 基准数据获取成功")
            self.log(f"Chunk数据长度: {len(self.baseline_chunk)} 字符")
            
            # 解析基准数据
            if self.baseline_chunk:
                try:
                    decoded_data = base64.b64decode(self.baseline_chunk)
                    self.log(f"解码后长度: {len(decoded_data)} 字节")
                    
                    # 显示前20个float值
                    self.log("\n📈 基准参数值:")
                    float_count = min(20, len(decoded_data) // 4)
                    for i in range(float_count):
                        offset = i * 4
                        float_val = struct.unpack('<f', decoded_data[offset:offset+4])[0]
                        self.log(f"  参数 {i:2d}: {float_val:8.3f}")
                        
                    self.test_btn.config(state="normal")
                    self.compare_btn.config(state="normal")
                    
                except Exception as e:
                    self.log(f"❌ 解析基准数据失败: {e}")
            else:
                self.log("⚠️ 未找到chunk_data")
        else:
            self.log("❌ 获取VST滤镜信息失败")
            
    def test_parameter(self):
        """测试参数设置"""
        if not self.baseline_chunk:
            messagebox.showwarning("警告", "请先获取基准数据")
            return
            
        try:
            param_index = int(self.param_var.get())
            test_value = float(self.value_var.get())
        except ValueError:
            messagebox.showerror("错误", "参数索引必须是整数，测试值必须是数字")
            return
            
        self.log(f"🧪 测试设置参数 {param_index} = {test_value}")
        
        try:
            # 解码基准数据
            decoded_data = bytearray(base64.b64decode(self.baseline_chunk))
            
            # 检查参数索引是否有效
            if param_index * 4 + 4 > len(decoded_data):
                self.log(f"❌ 参数索引 {param_index} 超出范围")
                return
                
            # 修改指定参数
            offset = param_index * 4
            struct.pack_into('<f', decoded_data, offset, test_value)
            
            # 重新编码
            new_chunk_data = base64.b64encode(decoded_data).decode('utf-8')
            
            # 发送到OBS
            source_name = self.source_var.get()
            filter_name = self.filter_var.get()
            
            response = self.send_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": {
                    "chunk_data": new_chunk_data
                }
            })
            
            if response and response.get('requestStatus', {}).get('result'):
                self.log("✅ 参数设置成功")
                self.current_chunk_data = new_chunk_data
                
                # 验证设置是否生效
                time.sleep(0.5)  # 等待一下
                verify_response = self.send_request("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                })
                
                if verify_response and verify_response.get('requestStatus', {}).get('result'):
                    verify_data = verify_response.get('responseData', {})
                    verify_chunk = verify_data.get('filterSettings', {}).get('chunk_data', '')
                    
                    if verify_chunk == new_chunk_data:
                        self.log("✅ 参数设置已确认生效")
                    else:
                        self.log("⚠️ 参数设置可能未完全生效")
                        
            else:
                self.log("❌ 参数设置失败")
                
        except Exception as e:
            self.log(f"❌ 测试参数时出错: {e}")
            
    def compare_changes(self):
        """对比变化"""
        if not self.baseline_chunk or not self.current_chunk_data:
            messagebox.showwarning("警告", "请先获取基准数据并测试参数")
            return
            
        self.log("🔍 对比参数变化:")
        
        try:
            baseline_data = base64.b64decode(self.baseline_chunk)
            current_data = base64.b64decode(self.current_chunk_data)
            
            if len(baseline_data) != len(current_data):
                self.log("⚠️ 数据长度不一致")
                return
                
            changes_found = 0
            float_count = len(baseline_data) // 4
            
            for i in range(float_count):
                offset = i * 4
                baseline_val = struct.unpack('<f', baseline_data[offset:offset+4])[0]
                current_val = struct.unpack('<f', current_data[offset:offset+4])[0]
                
                if abs(baseline_val - current_val) > 0.001:  # 允许小的浮点误差
                    self.log(f"  🔄 参数 {i:2d}: {baseline_val:8.3f} → {current_val:8.3f}")
                    changes_found += 1
                    
            if changes_found == 0:
                self.log("ℹ️ 未检测到参数变化")
            else:
                self.log(f"✅ 检测到 {changes_found} 个参数变化")
                
        except Exception as e:
            self.log(f"❌ 对比变化时出错: {e}")
            
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = VSTDirectControlTest()
    app.run()
