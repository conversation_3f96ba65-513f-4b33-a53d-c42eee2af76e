#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎛️ Graillon界面开启器
解决VST插件需要先打开界面才能访问参数的问题
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import websocket
import threading
import time
from datetime import datetime

class GraillonInterfaceOpener:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎛️ Graillon界面开启器")
        self.root.geometry("1000x700")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0

        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 OBS连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 操作区域
        action_frame = ttk.LabelFrame(main_frame, text="🎛️ VST插件操作", padding="10")
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：基本信息
        row1 = ttk.Frame(action_frame)
        row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(row1, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(row1, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row1, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(row1, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # 第二行：操作按钮
        row2 = ttk.Frame(action_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        self.check_status_btn = ttk.Button(row2, text="📊 检查当前状态", 
                                          command=self.check_current_status, state="disabled")
        self.check_status_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.enable_filter_btn = ttk.Button(row2, text="✅ 启用VST滤镜", 
                                           command=self.enable_vst_filter, state="disabled")
        self.enable_filter_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.open_interface_btn = ttk.Button(row2, text="🎛️ 打开插件界面", 
                                            command=self.open_plugin_interface, state="disabled")
        self.open_interface_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 第三行：参数操作
        row3 = ttk.Frame(action_frame)
        row3.pack(fill=tk.X, pady=(0, 5))
        
        self.get_params_btn = ttk.Button(row3, text="🔍 获取参数", 
                                        command=self.get_parameters, state="disabled")
        self.get_params_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.monitor_btn = ttk.Button(row3, text="📡 监控变化", 
                                     command=self.start_monitoring, state="disabled")
        self.monitor_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_monitor_btn = ttk.Button(row3, text="⏹️ 停止监控", 
                                          command=self.stop_monitoring, state="disabled")
        self.stop_monitor_btn.pack(side=tk.LEFT)
        
        # 第四行：测试操作
        row4 = ttk.Frame(action_frame)
        row4.pack(fill=tk.X)
        
        ttk.Label(row4, text="测试参数:").pack(side=tk.LEFT)
        self.param_var = tk.StringVar()
        self.param_entry = ttk.Entry(row4, textvariable=self.param_var, width=15)
        self.param_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row4, text="测试值:").pack(side=tk.LEFT)
        self.value_var = tk.StringVar(value="12")
        ttk.Entry(row4, textvariable=self.value_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        self.test_param_btn = ttk.Button(row4, text="🧪 测试参数", 
                                        command=self.test_parameter, state="disabled")
        self.test_param_btn.pack(side=tk.LEFT)
        
        # 说明区域
        info_frame = ttk.LabelFrame(main_frame, text="💡 使用说明", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_text = """🎯 解决VST插件参数访问问题的步骤：

1. 连接OBS
2. 检查当前状态 - 查看VST插件当前状态
3. 启用VST滤镜 - 确保滤镜处于启用状态
4. 打开插件界面 - 这是关键步骤！让VST插件加载完整参数
5. 获取参数 - 现在应该能看到所有音调参数了
6. 监控变化 - 实时监控参数变化
7. 测试参数 - 测试发现的参数

⚠️ 重要：很多VST插件需要先打开界面才会暴露完整的参数列表！"""
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(anchor=tk.W)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 操作日志", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.last_params = {}
        
        self.log("🎛️ Graillon界面开启器启动")
        self.log("这个工具专门解决VST插件需要打开界面才能访问参数的问题")
        self.log("=" * 60)
        self.log("🚀 请先点击'连接OBS'开始")
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    # WebSocket握手
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        # 启用所有按钮
        buttons = [self.check_status_btn, self.enable_filter_btn, self.open_interface_btn,
                  self.get_params_btn, self.monitor_btn, self.test_param_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🎉 所有功能已启用！")
        self.log("💡 建议按顺序操作：检查状态 → 启用滤镜 → 打开界面 → 获取参数")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.monitoring:
            self.stop_monitoring()
            
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        # 禁用所有按钮
        buttons = [self.check_status_btn, self.enable_filter_btn, self.open_interface_btn,
                  self.get_params_btn, self.monitor_btn, self.stop_monitor_btn, self.test_param_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=10):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            self.log(f"❌ 请求失败: {e}")
            return None

    def check_current_status(self):
        """检查当前状态"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("📊 检查VST插件当前状态...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            enabled = filter_data.get('filterEnabled', False)
            settings = filter_data.get('filterSettings', {})

            status_icon = "✅" if enabled else "❌"
            self.log(f"{status_icon} VST滤镜状态: {'启用' if enabled else '禁用'}")

            self.log(f"📊 当前参数数量: {len(settings)}")
            self.log("📋 参数列表:")

            param_count = 0
            for key, value in settings.items():
                param_count += 1
                if key == 'plugin_path':
                    self.log(f"  📁 {param_count}. {key}: {value}")
                elif key in ['chunk_data', 'chunk_hash']:
                    self.log(f"  💾 {param_count}. {key}: [二进制数据]")
                else:
                    self.log(f"  🎚️ {param_count}. {key}: {value}")

            if param_count <= 3:
                self.log("⚠️ 参数数量很少，可能需要打开插件界面来加载完整参数")
            else:
                self.log("✅ 参数数量正常，插件可能已完全加载")

        else:
            self.log("❌ 获取VST插件状态失败")

    def enable_vst_filter(self):
        """启用VST滤镜"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("✅ 启用VST滤镜...")

        response = self.send_request("SetSourceFilterEnabled", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterEnabled": True
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ VST滤镜已启用")

            # 验证启用状态
            time.sleep(0.5)
            self.check_current_status()
        else:
            self.log("❌ 启用VST滤镜失败")

    def open_plugin_interface(self):
        """尝试打开插件界面"""
        self.log("🎛️ 尝试打开VST插件界面...")
        self.log("💡 这个操作可能需要手动完成")

        # 显示提示对话框
        messagebox.showinfo(
            "打开插件界面",
            "请手动执行以下步骤：\n\n"
            "1. 在OBS中找到'媒体源'\n"
            "2. 右键点击 → 滤镜\n"
            "3. 选择'VST 2.x 插件'\n"
            "4. 点击'打开插件界面'按钮\n"
            "5. 等待Graillon界面打开\n"
            "6. 回到这个程序点击'获取参数'\n\n"
            "这样可以让VST插件加载完整的参数列表"
        )

        self.log("📋 请按照弹出对话框的说明手动打开插件界面")
        self.log("🔄 完成后请点击'获取参数'查看是否有更多参数")

    def get_parameters(self):
        """获取参数"""
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log("🔍 获取VST插件参数...")

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            filter_data = response.get('responseData', {})
            settings = filter_data.get('filterSettings', {})

            self.log(f"📊 发现 {len(settings)} 个参数:")
            self.log("=" * 50)

            numeric_params = []
            param_count = 0

            for key, value in settings.items():
                param_count += 1

                if key == 'plugin_path':
                    self.log(f"📁 {param_count:2d}. {key}: {value}")
                elif key in ['chunk_data', 'chunk_hash']:
                    self.log(f"💾 {param_count:2d}. {key}: [二进制数据]")
                else:
                    try:
                        num_value = float(value)
                        numeric_params.append(key)
                        self.log(f"🎚️ {param_count:2d}. {key}: {num_value}")

                        # 分析可能的参数用途
                        key_lower = key.lower()
                        if any(word in key_lower for word in ['pitch', 'tune', 'transpose', 'semitone']):
                            self.log(f"     🎵 -> 可能是音调参数")
                        elif any(word in key_lower for word in ['formant', 'voice', 'vocal']):
                            self.log(f"     🗣️ -> 可能是声音特征参数")
                        elif any(word in key_lower for word in ['mix', 'wet', 'dry', 'blend']):
                            self.log(f"     🔀 -> 可能是混合参数")

                    except (ValueError, TypeError):
                        self.log(f"📝 {param_count:2d}. {key}: {value}")

            self.log("=" * 50)
            self.log(f"🎯 发现 {len(numeric_params)} 个可测试的数值参数")

            if numeric_params:
                self.log("📋 可测试参数:")
                for param in numeric_params:
                    self.log(f"   - {param}")

                # 自动填入第一个参数到测试框
                if numeric_params:
                    self.param_var.set(numeric_params[0])
                    self.log(f"💡 已自动选择参数: {numeric_params[0]}")
            else:
                self.log("⚠️ 未发现可测试的数值参数")
                self.log("💡 建议:")
                self.log("   1. 确保已打开插件界面")
                self.log("   2. 在插件界面中调整一些参数")
                self.log("   3. 再次点击'获取参数'")

        else:
            self.log("❌ 获取参数失败")

    def start_monitoring(self):
        """开始监控参数变化"""
        if self.monitoring:
            self.log("⚠️ 监控已在运行中")
            return

        self.monitoring = True
        self.monitor_btn.config(state="disabled")
        self.stop_monitor_btn.config(state="normal")

        self.log("📡 开始监控参数变化...")
        self.log("💡 请在OBS中手动调整VST插件参数，这里会实时显示变化")

        # 获取初始参数状态
        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        response = self.send_request("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if response and response.get('requestStatus', {}).get('result'):
            settings = response.get('responseData', {}).get('filterSettings', {})
            self.last_params = settings.copy()
            self.log(f"📊 建立监控基线，跟踪 {len(settings)} 个参数")

        def monitor_loop():
            while self.monitoring and self.is_connected:
                try:
                    response = self.send_request("GetSourceFilter", {
                        "sourceName": source_name,
                        "filterName": filter_name
                    }, timeout=2)

                    if response and response.get('requestStatus', {}).get('result'):
                        current_settings = response.get('responseData', {}).get('filterSettings', {})

                        # 检查变化
                        for key, value in current_settings.items():
                            if key in ['plugin_path', 'chunk_data', 'chunk_hash']:
                                continue

                            if key in self.last_params:
                                old_value = self.last_params[key]

                                try:
                                    old_num = float(old_value)
                                    new_num = float(value)

                                    if abs(new_num - old_num) > 0.0001:
                                        diff = new_num - old_num
                                        self.root.after(0, self.log,
                                            f"🎚️ 参数变化: {key} = {old_num:.3f} → {new_num:.3f} ({diff:+.3f})")
                                        self.last_params[key] = value

                                except (ValueError, TypeError):
                                    if str(value) != str(old_value):
                                        self.root.after(0, self.log,
                                            f"📝 参数变化: {key} = {old_value} → {value}")
                                        self.last_params[key] = value
                            else:
                                # 新参数出现
                                self.root.after(0, self.log, f"🆕 新参数: {key} = {value}")
                                self.last_params[key] = value

                    time.sleep(0.5)  # 每0.5秒检查一次

                except Exception as e:
                    self.root.after(0, self.log, f"❌ 监控错误: {e}")
                    time.sleep(2)

        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return

        self.monitoring = False
        self.monitor_btn.config(state="normal")
        self.stop_monitor_btn.config(state="disabled")

        self.log("⏹️ 停止监控参数变化")

    def test_parameter(self):
        """测试参数"""
        param_name = self.param_var.get().strip()
        if not param_name:
            self.log("❌ 请输入要测试的参数名称")
            return

        try:
            test_value = float(self.value_var.get())
        except ValueError:
            self.log("❌ 测试值必须是数字")
            return

        source_name = self.source_var.get()
        filter_name = self.filter_var.get()

        self.log(f"🧪 测试参数: {param_name} = {test_value}")

        response = self.send_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": {
                param_name: test_value
            }
        })

        if response and response.get('requestStatus', {}).get('result'):
            self.log("✅ 参数设置成功")

            # 验证设置
            time.sleep(0.5)
            verify_response = self.send_request("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            })

            if verify_response and verify_response.get('requestStatus', {}).get('result'):
                verify_settings = verify_response.get('responseData', {}).get('filterSettings', {})
                if param_name in verify_settings:
                    actual_value = verify_settings[param_name]
                    self.log(f"📊 验证值: {actual_value}")

                    try:
                        if abs(float(actual_value) - test_value) < 0.01:
                            self.log("🎉 参数设置已确认生效！")
                            self.log("🎧 请检查音频是否有变化")
                        else:
                            self.log(f"⚠️ 值不匹配 (期望: {test_value}, 实际: {actual_value})")
                    except (ValueError, TypeError):
                        self.log(f"📝 参数已设置为: {actual_value}")
                else:
                    self.log("❌ 验证时未找到参数")
        else:
            self.log("❌ 参数设置失败")

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GraillonInterfaceOpener()
    app.run()
