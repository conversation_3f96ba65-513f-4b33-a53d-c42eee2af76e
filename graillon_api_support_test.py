#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 Graillon API支持测试器
专门测试Graillon 3是否真的支持通过OBS WebSocket API进行参数修改
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import websocket
import threading
import time
from datetime import datetime

class GraillonAPISupportTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 Graillon API支持测试器")
        self.root.geometry("1000x700")

        # 连接状态
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        
        # 测试状态
        self.testing = False
        self.test_results = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接区域
        conn_frame = ttk.LabelFrame(main_frame, text="🔗 连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(conn_frame, text="❌ 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接OBS", command=self.connect_obs)
        self.connect_btn.pack(side=tk.RIGHT)
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="⚙️ 配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X)
        
        ttk.Label(row1, text="音频源:").pack(side=tk.LEFT)
        self.source_var = tk.StringVar(value="媒体源")
        ttk.Entry(row1, textvariable=self.source_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(row1, text="VST滤镜:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar(value="VST 2.x 插件")
        ttk.Entry(row1, textvariable=self.filter_var, width=15).pack(side=tk.LEFT, padx=(5, 10))
        
        # API支持测试区域
        test_frame = ttk.LabelFrame(main_frame, text="🧪 API支持测试", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        row2 = ttk.Frame(test_frame)
        row2.pack(fill=tk.X, pady=(0, 5))
        
        self.get_params_btn = ttk.Button(row2, text="📊 获取当前参数", 
                                        command=self.get_current_params, state="disabled")
        self.get_params_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_obvious_btn = ttk.Button(row2, text="🎯 测试明显参数", 
                                          command=self.test_obvious_params, state="disabled")
        self.test_obvious_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.manual_test_btn = ttk.Button(row2, text="👁️ 手动对比测试", 
                                         command=self.manual_comparison_test, state="disabled")
        self.manual_test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        row3 = ttk.Frame(test_frame)
        row3.pack(fill=tk.X)
        
        self.comprehensive_btn = ttk.Button(row3, text="🔬 综合API支持测试", 
                                           command=self.comprehensive_api_test, state="disabled")
        self.comprehensive_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.final_verdict_btn = ttk.Button(row3, text="⚖️ 最终判决", 
                                           command=self.final_verdict, state="disabled")
        self.final_verdict_btn.pack(side=tk.LEFT)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="📋 测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        self.log("🧪 Graillon API支持测试器")
        self.log("专门测试Graillon 3是否真的支持通过OBS WebSocket API进行参数修改")
        self.log("=" * 70)
        self.log("🎯 测试目标:")
        self.log("1. 验证API是否能真正修改Graillon参数")
        self.log("2. 确认参数修改是否反映在界面上")
        self.log("3. 验证参数修改是否产生实际音频效果")
        self.log("4. 判断VST是否只是'假装'接受API调用")
        self.log("=" * 70)
        self.log("💡 从你的截图可以看到Graillon 3有这些主要控制:")
        self.log("   🎚️ PITCH-SHIFT: 主要的音调调节大旋钮")
        self.log("   🎛️ PITCH ENGINE: G2, G3, T1 滑块")
        self.log("   🔧 CORRECTION: Smooth, Inertia, Snap Min/Max")
        self.log("   🗣️ Formants: 共振峰控制")
        self.log("   🔊 OUTPUT: Wet/Dry, Gain, Amount")
        self.log("=" * 70)
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_msg)
        self.result_text.see(tk.END)
        
    def connect_obs(self):
        """连接OBS"""
        if not self.is_connected:
            self.log("🔗 正在连接到OBS...")
            self.connect_btn.config(state="disabled", text="连接中...")
            
            def connect_thread():
                try:
                    self.ws = websocket.create_connection("ws://localhost:4455", timeout=5)
                    
                    hello_raw = self.ws.recv()
                    hello_data = json.loads(hello_raw)
                    
                    if hello_data.get("op") != 0:
                        raise ValueError("未收到Hello消息")
                    
                    identify_payload = {
                        "op": 1,
                        "d": {
                            "rpcVersion": hello_data.get("d", {}).get("rpcVersion", 1),
                            "eventSubscriptions": 33
                        }
                    }
                    self.ws.send(json.dumps(identify_payload))
                    
                    identified_raw = self.ws.recv()
                    identified_data = json.loads(identified_raw)
                    
                    if identified_data.get("op") != 2:
                        raise ValueError("未收到Identified消息")
                    
                    self.root.after(0, self.on_connect_success)
                    
                except Exception as e:
                    self.root.after(0, self.on_connect_error, str(e))
                    
            threading.Thread(target=connect_thread, daemon=True).start()
        else:
            self.disconnect_obs()
            
    def on_connect_success(self):
        """连接成功"""
        self.is_connected = True
        self.status_label.config(text="✅ 已连接", foreground="green")
        self.connect_btn.config(text="断开连接", state="normal")
        
        buttons = [self.get_params_btn, self.test_obvious_btn, self.manual_test_btn,
                  self.comprehensive_btn, self.final_verdict_btn]
        for btn in buttons:
            btn.config(state="normal")
        
        self.log("✅ 成功连接到OBS WebSocket")
        self.log("🚀 现在可以开始API支持测试了！")
        self.log("💡 建议先点击'获取当前参数'查看基础状态")
        
    def on_connect_error(self, error):
        """连接失败"""
        self.status_label.config(text="❌ 连接失败", foreground="red")
        self.connect_btn.config(text="连接OBS", state="normal")
        self.log(f"❌ 连接失败: {error}")
        
    def disconnect_obs(self):
        """断开连接"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
            self.ws = None
            
        self.is_connected = False
        self.status_label.config(text="❌ 未连接", foreground="red")
        self.connect_btn.config(text="连接OBS")
        
        buttons = [self.get_params_btn, self.test_obvious_btn, self.manual_test_btn,
                  self.comprehensive_btn, self.final_verdict_btn]
        for btn in buttons:
            btn.config(state="disabled")
        
        self.log("🔌 已断开OBS连接")
        
    def send_request(self, request_type, request_data=None, timeout=5):
        """发送请求并获取响应"""
        if not self.is_connected or not self.ws:
            return None
            
        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        
        payload = {
            "op": 6,
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            self.ws.send(json.dumps(payload))
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)
                    
                    if (response_data.get("op") == 7 and 
                        response_data.get("d", {}).get("requestId") == request_id):
                        return response_data.get("d", {})
                        
                except websocket.WebSocketTimeoutException:
                    continue
                except:
                    continue
                    
            return None
            
        except Exception as e:
            return None

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GraillonAPISupportTest()
    app.run()
